/**
 * Tool Creation Manager
 * 
 * Manages the creation of tool records before AI generation jobs
 * to satisfy foreign key constraints and maintain data integrity.
 */

import { createClient } from '@supabase/supabase-js';
import { BulkToolData } from '@/lib/types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Interface for tool creation result
interface ToolCreationResult {
  id: string;
  name: string;
  website: string;
}

// Interface for tool status update data
interface ToolStatusUpdateData {
  ai_generation_status: 'completed' | 'failed' | 'pending' | 'processing';
  updated_at: string;
  last_ai_update?: string;
  error_message?: string;
}

export class ToolCreationManager {
  private supabase = createClient(supabaseUrl, supabaseServiceKey);

  /**
   * Create or find tool record for a URL
   */
  async ensureToolExists(toolData: BulkToolData): Promise<string> {
    try {
      // First, try to find existing tool by URL
      const existingTool = await this.findToolByUrl(toolData.url);
      if (existingTool) {
        console.log(`✅ Found existing tool: ${existingTool.id} for ${toolData.url}`);
        return existingTool.id;
      }

      // Try to find by name as well (in case URL format changed)
      const toolName = toolData.providedData?.name || this.extractNameFromUrl(toolData.url);
      const existingByName = await this.findToolByName(toolName);
      if (existingByName) {
        console.log(`✅ Found existing tool by name: ${existingByName.id} for ${toolName} (updating URL)`);
        // Update the existing tool with the new URL
        await this.updateToolUrl(existingByName.id, toolData.url);
        return existingByName.id;
      }

      // Create new tool record
      const newTool = await this.createToolRecord(toolData);
      console.log(`✅ Created new tool: ${newTool.id} for ${toolData.url}`);
      return newTool.id;

    } catch (error) {
      console.error(`❌ Failed to ensure tool exists for ${toolData.url}:`, error);
      throw error;
    }
  }

  /**
   * Find existing tool by URL
   */
  private async findToolByUrl(url: string): Promise<ToolCreationResult | null> {
    const { data, error } = await this.supabase
      .from('tools')
      .select('id, name, website, content_status')
      .eq('website', url)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new Error(`Failed to search for existing tool: ${error.message}`);
    }

    return data;
  }

  /**
   * Find existing tool by name
   */
  private async findToolByName(name: string): Promise<ToolCreationResult | null> {
    const { data, error } = await this.supabase
      .from('tools')
      .select('id, name, website, content_status')
      .eq('name', name)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new Error(`Failed to search for existing tool by name: ${error.message}`);
    }

    return data;
  }

  /**
   * Update tool URL
   */
  private async updateToolUrl(toolId: string, newUrl: string): Promise<void> {
    const { error } = await this.supabase
      .from('tools')
      .update({
        website: newUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', toolId);

    if (error) {
      throw new Error(`Failed to update tool URL: ${error.message}`);
    }
  }

  /**
   * Handle name conflict by updating existing tool
   */
  private async handleNameConflict(toolName: string, toolData: BulkToolData): Promise<ToolCreationResult> {
    // Find the existing tool by name
    const existingTool = await this.findToolByName(toolName);
    if (!existingTool) {
      throw new Error(`Tool with name "${toolName}" not found for conflict resolution`);
    }

    console.log(`🔄 Updating existing tool ${existingTool.id} with new data from ${toolData.url}`);

    // Update the existing tool with new data
    const updateData = {
      website: toolData.url,
      description: toolData.providedData?.description || `AI tool at ${toolData.url}`,
      submission_type: 'admin',
      submission_source: 'bulk_processing',
      ai_generation_status: 'pending',
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.supabase
      .from('tools')
      .update(updateData)
      .eq('id', existingTool.id)
      .select('id, name, website')
      .single();

    if (error) {
      throw new Error(`Failed to update existing tool: ${error.message}`);
    }

    return data;
  }

  /**
   * Create new tool record
   */
  private async createToolRecord(toolData: BulkToolData): Promise<ToolCreationResult> {
    // Generate basic tool data
    const toolName = toolData.providedData?.name || this.extractNameFromUrl(toolData.url);
    const slug = this.generateSlug(toolName);

    const newToolData = {
      id: this.generateUUID(), // Generate UUID for id field
      name: toolName,
      slug: slug,
      link: `/tools/${slug}`, // Generate link field
      website: toolData.url,
      description: toolData.providedData?.description || `AI tool at ${toolData.url}`,
      content_status: 'draft',
      submission_type: 'admin', // Valid values: 'admin', 'user_url', 'user_full'
      submission_source: 'bulk_processing',
      ai_generation_status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.supabase
      .from('tools')
      .insert(newToolData)
      .select('id, name, website')
      .single();

    if (error) {
      // Handle duplicate constraints by adding suffix
      if (error.code === '23505') {
        if (error.message.includes('name') || error.message.includes('idx_tools_name_unique')) {
          // Handle duplicate name - overwrite existing tool
          console.log(`🔄 Name conflict detected for "${toolName}" - updating existing tool`);
          return await this.handleNameConflict(toolName, toolData);
        }

        const retryData = { ...newToolData };

        if (error.message.includes('slug') || error.message.includes('idx_tools_slug')) {
          // Handle duplicate slug
          const uniqueSlug = `${slug}-${Date.now()}`;
          retryData.slug = uniqueSlug;
          retryData.link = `/tools/${uniqueSlug}`;
          console.log(`🔄 Retrying with unique slug: ${uniqueSlug}`);
        } else {
          // Handle other unique constraint violations
          const timestamp = Date.now();
          retryData.name = `${toolName} (${timestamp})`;
          retryData.slug = `${slug}-${timestamp}`;
          retryData.link = `/tools/${slug}-${timestamp}`;
          console.log(`🔄 Retrying with timestamp suffix: ${timestamp}`);
        }

        const { data: retryResult, error: retryError } = await this.supabase
          .from('tools')
          .insert(retryData)
          .select('id, name, website')
          .single();

        if (retryError) {
          throw new Error(`Failed to create tool record (retry): ${retryError.message}`);
        }

        return retryResult;
      }

      throw new Error(`Failed to create tool record: ${error.message}`);
    }

    return data;
  }

  /**
   * Extract tool name from URL
   */
  private extractNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace('www.', '');
      const name = domain.split('.')[0];
      
      // Capitalize first letter
      return name.charAt(0).toUpperCase() + name.slice(1);
    } catch {
      return 'AI Tool';
    }
  }

  /**
   * Generate URL-safe slug
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50); // Limit length
  }

  /**
   * Generate UUID
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Batch create tools for multiple URLs
   */
  async ensureToolsExist(toolDataArray: BulkToolData[]): Promise<Map<string, string>> {
    const urlToToolIdMap = new Map<string, string>();
    
    console.log(`🔧 Ensuring ${toolDataArray.length} tools exist...`);

    // Process tools in batches to avoid overwhelming the database
    const batchSize = 5;
    for (let i = 0; i < toolDataArray.length; i += batchSize) {
      const batch = toolDataArray.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (toolData) => {
        try {
          const toolId = await this.ensureToolExists(toolData);
          urlToToolIdMap.set(toolData.url, toolId);
          return { url: toolData.url, toolId, success: true };
        } catch (error) {
          console.error(`Failed to ensure tool exists for ${toolData.url}:`, error);
          return { url: toolData.url, error: (error as Error).message, success: false };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      
      // Log batch results
      const successful = batchResults.filter(r => r.success).length;
      const failed = batchResults.filter(r => !r.success).length;
      console.log(`📦 Batch ${Math.floor(i / batchSize) + 1}: ${successful} successful, ${failed} failed`);

      // Small delay between batches
      if (i + batchSize < toolDataArray.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`✅ Tool creation completed: ${urlToToolIdMap.size} tools ready`);
    return urlToToolIdMap;
  }

  /**
   * Update tool with AI generation job ID
   */
  async linkToolToJob(toolId: string, jobId: string): Promise<void> {
    const { error } = await this.supabase
      .from('tools')
      .update({
        ai_generation_job_id: jobId,
        ai_generation_status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', toolId);

    if (error) {
      console.error(`Failed to link tool ${toolId} to job ${jobId}:`, error);
      // Don't throw here as this is not critical for the job processing
    }
  }

  /**
   * Update tool status after job completion
   */
  async updateToolStatus(toolId: string, status: 'completed' | 'failed', errorMessage?: string): Promise<void> {
    const updateData: ToolStatusUpdateData = {
      ai_generation_status: status,
      updated_at: new Date().toISOString()
    };

    if (status === 'completed') {
      updateData.last_ai_update = new Date().toISOString();
    } else if (status === 'failed' && errorMessage) {
      updateData.error_message = errorMessage;
    }

    const { error: updateError } = await this.supabase
      .from('tools')
      .update(updateData)
      .eq('id', toolId);

    if (updateError) {
      console.error(`Failed to update tool ${toolId} status to ${status}:`, updateError);
    }
  }
}

export const getToolCreationManager = () => new ToolCreationManager();
