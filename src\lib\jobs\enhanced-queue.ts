import { createClient } from '@supabase/supabase-js';
import { 
  Job, 
  JobType, 
  JobStatus, 
  JobPriority, 
  JobQueue, 
  JobOptions, 
  ProgressDetails,
  QueueStats,
  JobHistoryFilter,
  JobControlOptions
} from './types';
import { getProgressTracker } from './progress-tracker';
import { getWebSocketManager } from './websocket-manager';

/**
 * Enhanced Job Queue with Database Persistence and Real-time Updates
 * 
 * Replaces the in-memory queue with a database-backed system that provides:
 * - Persistent job storage
 * - Real-time progress tracking
 * - Pause/resume/stop controls
 * - WebSocket-based updates
 * - Job history and audit trail
 */
export class EnhancedJobQueue implements JobQueue {
  private supabase;
  private progressTracker;
  private webSocketManager;
  private processing = false;
  private maxConcurrent: number;
  private currentlyProcessing = 0;
  private processingInterval: NodeJS.Timeout | null = null;

  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    this.progressTracker = getProgressTracker();
    this.webSocketManager = getWebSocketManager();
  }

  /**
   * Add a new job to the queue
   */
  async add(type: JobType, data: any, options: JobOptions = {}): Promise<Job> {
    try {
      const job: Job = {
        id: this.generateId(),
        type,
        data,
        status: JobStatus.PENDING,
        priority: options.priority || JobPriority.NORMAL,
        attempts: 0,
        maxAttempts: options.maxAttempts || parseInt(process.env.JOB_RETRY_ATTEMPTS || '3'),
        createdAt: new Date(),
        updatedAt: new Date(),
        scheduledFor: options.scheduledFor,
        progress: 0,
        canPause: this.isJobPausable(type),
        canResume: false,
        canStop: true,
        toolId: data.toolId, // Must be a valid tool ID, not URL
        tags: this.generateJobTags(type, data),
      };

      // Store job in database
      await this.storeJobInDatabase(job);

      // Initialize progress tracking
      await this.progressTracker.initializeJob(job);

      // Start processing if not already running
      if (!this.processing) {
        this.startProcessing();
      }

      // Trigger immediate processing attempt
      setImmediate(() => this.process());

      console.log(`✅ Job ${job.id} (${job.type}) added to queue`);
      return job;
    } catch (error) {
      console.error('Failed to add job to queue:', error);
      throw error;
    }
  }

  /**
   * Process pending jobs with atomic claiming to prevent duplicates
   */
  async process(): Promise<void> {
    if (this.currentlyProcessing >= this.maxConcurrent) {
      return;
    }

    try {
      const availableSlots = this.maxConcurrent - this.currentlyProcessing;
      if (availableSlots <= 0) {
        return;
      }

      // Atomically claim jobs by updating their status to PROCESSING
      // This prevents other queue instances from picking up the same jobs
      const { data: claimedJobs, error } = await this.supabase
        .rpc('claim_pending_jobs', {
          max_jobs: availableSlots,
          claim_time: new Date().toISOString()
        });

      if (error) {
        console.error('Failed to claim pending jobs:', error);
        // Fallback to old method if RPC doesn't exist
        return await this.processPendingJobsFallback();
      }

      if (!claimedJobs || claimedJobs.length === 0) {
        return;
      }

      console.log(`🎯 Claimed ${claimedJobs.length} jobs for processing`);

      // Process each claimed job
      for (const jobData of claimedJobs) {
        if (this.currentlyProcessing >= this.maxConcurrent) {
          break;
        }

        const job = this.mapDatabaseJobToJob(jobData);
        this.processJob(job); // Don't await - process in parallel (but jobs are already claimed)
      }
    } catch (error) {
      console.error('Error in job processing:', error);
    }
  }

  /**
   * Fallback method for processing jobs without atomic claiming
   */
  private async processPendingJobsFallback(): Promise<void> {
    try {
      // Get pending jobs from database
      const { data: pendingJobs, error } = await this.supabase
        .from('ai_generation_jobs')
        .select('*')
        .eq('status', JobStatus.PENDING)
        .or(`scheduled_for.is.null,scheduled_for.lte.${new Date().toISOString()}`)
        .order('priority', { ascending: false })
        .order('created_at', { ascending: true })
        .limit(this.maxConcurrent - this.currentlyProcessing);

      if (error) {
        console.error('Failed to fetch pending jobs:', error);
        return;
      }

      if (!pendingJobs || pendingJobs.length === 0) {
        return;
      }

      // Process each job with immediate status update to prevent duplicates
      for (const jobData of pendingJobs) {
        if (this.currentlyProcessing >= this.maxConcurrent) {
          break;
        }

        // Immediately update status to PROCESSING to prevent other instances from picking it up
        const { error: updateError } = await this.supabase
          .from('ai_generation_jobs')
          .update({
            status: JobStatus.PROCESSING,
            updated_at: new Date().toISOString()
          })
          .eq('id', jobData.id)
          .eq('status', JobStatus.PENDING); // Only update if still pending

        if (updateError) {
          console.warn(`Failed to claim job ${jobData.id}:`, updateError);
          continue; // Skip this job, it might have been claimed by another instance
        }

        const job = this.mapDatabaseJobToJob({ ...jobData, status: JobStatus.PROCESSING });
        this.processJob(job); // Don't await - process in parallel
      }
    } catch (error) {
      console.error('Error in fallback job processing:', error);
    }
  }

  /**
   * Get a specific job by ID
   */
  async getJob(id: string): Promise<Job | null> {
    try {
      const { data, error } = await this.supabase
        .from('ai_generation_jobs')
        .select('*')
        .eq('id', id)
        .single();

      if (error || !data) {
        return null;
      }

      return this.mapDatabaseJobToJob(data);
    } catch (error) {
      console.error(`Failed to get job ${id}:`, error);
      return null;
    }
  }

  /**
   * Get jobs by status
   */
  async getJobs(status?: JobStatus): Promise<Job[]> {
    try {
      let query = this.supabase
        .from('ai_generation_jobs')
        .select('*')
        .order('created_at', { ascending: false });

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Failed to get jobs:', error);
        return [];
      }

      return (data || []).map(jobData => this.mapDatabaseJobToJob(jobData));
    } catch (error) {
      console.error('Failed to get jobs:', error);
      return [];
    }
  }

  /**
   * Remove a job from the queue
   */
  async removeJob(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('ai_generation_jobs')
        .delete()
        .eq('id', id);

      if (error) {
        console.error(`Failed to remove job ${id}:`, error);
        return false;
      }

      // Clear progress tracking
      this.progressTracker.clearProgress(id);

      console.log(`🗑️ Job ${id} removed from queue`);
      return true;
    } catch (error) {
      console.error(`Failed to remove job ${id}:`, error);
      return false;
    }
  }

  /**
   * Retry a failed job or force retry a stuck retrying job
   */
  async retryJob(id: string): Promise<Job> {
    try {
      const job = await this.getJob(id);
      if (!job) {
        throw new Error(`Job ${id} not found`);
      }

      // Check if job can be retried
      const canRetry = job.status === JobStatus.FAILED ||
                      job.status === JobStatus.CANCELLED ||
                      this.isStuckRetrying(job);

      if (!canRetry) {
        throw new Error(`Job ${id} cannot be retried (status: ${job.status})`);
      }

      // Determine if this is a force retry for stuck job
      const isForceRetry = job.status === JobStatus.RETRYING;

      // Reset job for retry
      const updatedJob = {
        ...job,
        status: JobStatus.PENDING,
        attempts: isForceRetry ? job.attempts : 0, // Keep attempts for force retry, reset for failed jobs
        progress: 0,
        error: undefined,
        updatedAt: new Date(),
        scheduledFor: undefined,
      };

      await this.updateJobInDatabase(updatedJob);
      await this.progressTracker.updateJobStatus(id, JobStatus.PENDING);

      const retryType = isForceRetry ? 'force retry (stuck job)' : 'retry';
      console.log(`🔄 Job ${id} queued for ${retryType}`);
      return updatedJob;
    } catch (error) {
      console.error(`Failed to retry job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Check if a retrying job is stuck (retrying for more than 15 minutes)
   */
  private isStuckRetrying(job: Job): boolean {
    if (job.status !== JobStatus.RETRYING) return false;

    const now = new Date();
    const updatedAt = new Date(job.updatedAt);
    const minutesSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60);

    return minutesSinceUpdate > 15; // Consider stuck if retrying for more than 15 minutes
  }

  /**
   * Pause a job
   */
  async pauseJob(id: string): Promise<Job> {
    try {
      const job = await this.getJob(id);
      if (!job) {
        throw new Error(`Job ${id} not found`);
      }

      if (!job.canPause || job.status !== JobStatus.PROCESSING) {
        throw new Error(`Job ${id} cannot be paused (status: ${job.status})`);
      }

      job.status = JobStatus.PAUSED;
      job.canResume = true;
      job.canPause = false;
      job.updatedAt = new Date();

      await this.updateJobInDatabase(job);
      await this.progressTracker.updateJobStatus(id, JobStatus.PAUSED);

      console.log(`⏸️ Job ${id} paused`);
      return job;
    } catch (error) {
      console.error(`Failed to pause job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Resume a paused job
   */
  async resumeJob(id: string): Promise<Job> {
    try {
      const job = await this.getJob(id);
      if (!job) {
        throw new Error(`Job ${id} not found`);
      }

      if (!job.canResume || job.status !== JobStatus.PAUSED) {
        throw new Error(`Job ${id} cannot be resumed (status: ${job.status})`);
      }

      job.status = JobStatus.PENDING;
      job.canResume = false;
      job.canPause = true;
      job.updatedAt = new Date();

      await this.updateJobInDatabase(job);
      await this.progressTracker.updateJobStatus(id, JobStatus.PENDING);

      console.log(`▶️ Job ${id} resumed`);
      return job;
    } catch (error) {
      console.error(`Failed to resume job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Stop a job
   */
  async stopJob(id: string): Promise<Job> {
    try {
      const job = await this.getJob(id);
      if (!job) {
        throw new Error(`Job ${id} not found`);
      }

      if (!job.canStop) {
        throw new Error(`Job ${id} cannot be stopped`);
      }

      job.status = JobStatus.STOPPED;
      job.canPause = false;
      job.canResume = false;
      job.canStop = false;
      job.updatedAt = new Date();
      job.completedAt = new Date();

      await this.updateJobInDatabase(job);
      await this.progressTracker.updateJobStatus(id, JobStatus.STOPPED);

      console.log(`⏹️ Job ${id} stopped`);
      return job;
    } catch (error) {
      console.error(`Failed to stop job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update job progress
   */
  async updateProgress(id: string, progress: number, details?: ProgressDetails): Promise<void> {
    await this.progressTracker.updateProgress(id, progress, details);
  }

  /**
   * Get job history with filtering
   */
  async getJobHistory(limit = 50, offset = 0): Promise<Job[]> {
    try {
      const { data, error } = await this.supabase
        .from('ai_generation_jobs')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Failed to get job history:', error);
        return [];
      }

      return (data || []).map(jobData => this.mapDatabaseJobToJob(jobData));
    } catch (error) {
      console.error('Failed to get job history:', error);
      return [];
    }
  }

  /**
   * Get jobs by tool ID
   */
  async getJobsByTool(toolId: string): Promise<Job[]> {
    try {
      const { data, error } = await this.supabase
        .from('ai_generation_jobs')
        .select('*')
        .eq('tool_id', toolId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error(`Failed to get jobs for tool ${toolId}:`, error);
        return [];
      }

      return (data || []).map(jobData => this.mapDatabaseJobToJob(jobData));
    } catch (error) {
      console.error(`Failed to get jobs for tool ${toolId}:`, error);
      return [];
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<QueueStats> {
    try {
      const { data, error } = await this.supabase
        .from('ai_generation_jobs')
        .select('status, created_at, completed_at');

      if (error) {
        console.error('Failed to get queue stats:', error);
        return this.getEmptyStats();
      }

      const jobs = data || [];
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const processingJobs = jobs.filter(j => j.status === JobStatus.PROCESSING).length;
      const stats = {
        totalJobs: jobs.length,
        pendingJobs: jobs.filter(j => j.status === JobStatus.PENDING).length,
        processingJobs: processingJobs,
        completedJobs: jobs.filter(j => j.status === JobStatus.COMPLETED).length,
        failedJobs: jobs.filter(j => j.status === JobStatus.FAILED).length,
        pausedJobs: jobs.filter(j => j.status === JobStatus.PAUSED).length,
        activeJobs: processingJobs, // Active jobs are currently processing jobs
        averageProgress: 0, // TODO: Implement progress tracking for enhanced queue
        averageProcessingTime: this.calculateAverageProcessingTime(jobs),
        successRate: this.calculateSuccessRate(jobs),
        lastUpdated: now,
      };

      return stats;
    } catch (error) {
      console.error('Failed to get queue stats:', error);
      return this.getEmptyStats();
    }
  }

  /**
   * Process a single job
   */
  private async processJob(job: Job): Promise<void> {
    this.currentlyProcessing++;
    console.log(`🔄 Processing job ${job.id} (${job.type})`);

    try {
      // Update job status to processing
      job.status = JobStatus.PROCESSING;
      job.attempts++;
      job.updatedAt = new Date();

      await this.updateJobInDatabase(job);
      await this.progressTracker.updateJobStatus(job.id, JobStatus.PROCESSING);

      // Get job handler and process
      const handler = await this.getHandler(job.type);
      const result = await handler.handle(job);

      // Job completed successfully
      job.status = JobStatus.COMPLETED;
      job.result = result;
      job.completedAt = new Date();
      job.progress = 100;
      job.actualDuration = job.completedAt.getTime() - job.createdAt.getTime();

      await this.updateJobInDatabase(job);
      await this.progressTracker.updateProgress(job.id, 100, {
        phase: 'completed',
        currentStep: 'finished',
        totalSteps: 1,
        completedSteps: 1,
        message: 'Job completed successfully',
      });

      console.log(`✅ Job ${job.id} completed successfully`);
    } catch (error) {
      console.error(`❌ Job ${job.id} failed:`, error);

      job.error = error instanceof Error ? error.message : String(error);

      if (job.attempts < job.maxAttempts) {
        // Retry with exponential backoff
        job.status = JobStatus.RETRYING;
        const delay = Math.pow(2, job.attempts) * 1000;
        job.scheduledFor = new Date(Date.now() + delay);

        await this.progressTracker.updateJobStatus(job.id, JobStatus.RETRYING, {
          retryAttempt: job.attempts,
          nextRetryAt: job.scheduledFor,
        });
      } else {
        // Max attempts reached
        job.status = JobStatus.FAILED;
        job.completedAt = new Date();

        await this.progressTracker.updateJobStatus(job.id, JobStatus.FAILED, {
          finalError: job.error,
        });
      }

      await this.updateJobInDatabase(job);
    } finally {
      this.currentlyProcessing--;
    }
  }

  /**
   * Get job handler for specific job type
   */
  private async getHandler(type: JobType) {
    const { getJobHandler } = await import('./handlers');
    return getJobHandler(type);
  }

  /**
   * Start the processing loop
   */
  private startProcessing(): void {
    if (this.processing) return;

    this.processing = true;
    console.log('🚀 Starting enhanced job processing loop');

    this.processingInterval = setInterval(async () => {
      try {
        await this.process();
      } catch (error) {
        console.error('Job processing error:', error);
      }
    }, 2000); // Check every 2 seconds
  }

  /**
   * Stop the processing loop
   */
  stop(): void {
    this.processing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    console.log('⏹️ Enhanced job processing stopped');
  }

  /**
   * Generate unique job ID as UUID
   */
  private generateId(): string {
    // Generate a proper UUID v4 format for database compatibility
    // Using the same logic as bulk-engine.ts to ensure consistency
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Check if job type supports pausing
   */
  private isJobPausable(type: JobType): boolean {
    // Most job types can be paused except for quick operations
    const nonPausableTypes = [JobType.EMAIL_NOTIFICATION];
    return !nonPausableTypes.includes(type);
  }

  /**
   * Generate tags for job categorization
   */
  private generateJobTags(type: JobType, data: any): string[] {
    const tags: string[] = [type];

    if (data.url) {
      try {
        const domain = new URL(data.url).hostname;
        tags.push(`domain:${domain}`);
      } catch {
        // Invalid URL, skip domain tag
      }
    }

    if (data.category) {
      tags.push(`category:${data.category}`);
    }

    return tags;
  }

  /**
   * Store job in database
   */
  private async storeJobInDatabase(job: Job): Promise<void> {
    const jobData = {
      id: job.id,
      tool_id: job.toolId,
      job_type: job.type,
      status: job.status,
      progress: job.progress || 0,
      scraped_data: null,
      ai_prompts: null,
      ai_responses: null,
      error_logs: job.error ? [{ timestamp: new Date().toISOString(), error: job.error }] : null,
      created_at: job.createdAt.toISOString(),
      updated_at: job.updatedAt.toISOString(),
      completed_at: job.completedAt?.toISOString(),
      scheduled_for: job.scheduledFor?.toISOString(),
      priority: job.priority,
      attempts: job.attempts,
      max_attempts: job.maxAttempts,
      job_data: job.data,
      tags: job.tags,
      can_pause: job.canPause,
      can_resume: job.canResume,
      can_stop: job.canStop,
    };

    const { error } = await this.supabase
      .from('ai_generation_jobs')
      .insert(jobData);

    if (error) {
      console.error('Failed to store job in database:', error);
      throw error;
    }
  }

  /**
   * Update job in database
   */
  private async updateJobInDatabase(job: Job): Promise<void> {
    const updateData = {
      status: job.status,
      progress: job.progress || 0,
      updated_at: job.updatedAt.toISOString(),
      completed_at: job.completedAt?.toISOString(),
      scheduled_for: job.scheduledFor?.toISOString(),
      attempts: job.attempts,
      error_logs: job.error ? [{ timestamp: new Date().toISOString(), error: job.error }] : null,
      result: job.result,
      can_pause: job.canPause,
      can_resume: job.canResume,
      can_stop: job.canStop,
      actual_duration: job.actualDuration,
    };

    const { error } = await this.supabase
      .from('ai_generation_jobs')
      .update(updateData)
      .eq('id', job.id);

    if (error) {
      console.error(`Failed to update job ${job.id} in database:`, error);
      throw error;
    }
  }

  /**
   * Map database job data to Job interface
   */
  private mapDatabaseJobToJob(jobData: any): Job {
    return {
      id: jobData.id,
      type: jobData.job_type,
      data: jobData.job_data,
      status: jobData.status,
      priority: jobData.priority || JobPriority.NORMAL,
      attempts: jobData.attempts || 0,
      maxAttempts: jobData.max_attempts || 3,
      createdAt: new Date(jobData.created_at),
      updatedAt: new Date(jobData.updated_at),
      scheduledFor: jobData.scheduled_for ? new Date(jobData.scheduled_for) : undefined,
      completedAt: jobData.completed_at ? new Date(jobData.completed_at) : undefined,
      error: jobData.error_logs?.[0]?.error,
      result: jobData.result,
      progress: jobData.progress || 0,
      progressDetails: jobData.progress_log?.[jobData.progress_log?.length - 1]?.details,
      canPause: jobData.can_pause ?? true,
      canResume: jobData.can_resume ?? false,
      canStop: jobData.can_stop ?? true,
      toolId: jobData.tool_id,
      tags: jobData.tags || [],
      estimatedDuration: jobData.estimated_duration,
      actualDuration: jobData.actual_duration,
    };
  }

  /**
   * Calculate average processing time
   */
  private calculateAverageProcessingTime(jobs: any[]): number {
    const completedJobs = jobs.filter(j =>
      j.status === JobStatus.COMPLETED &&
      j.created_at &&
      j.completed_at
    );

    if (completedJobs.length === 0) return 0;

    const totalTime = completedJobs.reduce((sum, job) => {
      const start = new Date(job.created_at).getTime();
      const end = new Date(job.completed_at).getTime();
      return sum + (end - start);
    }, 0);

    return Math.round(totalTime / completedJobs.length);
  }

  /**
   * Calculate success rate
   */
  private calculateSuccessRate(jobs: any[]): number {
    const finishedJobs = jobs.filter(j =>
      j.status === JobStatus.COMPLETED ||
      j.status === JobStatus.FAILED
    );

    if (finishedJobs.length === 0) return 0;

    const successfulJobs = finishedJobs.filter(j => j.status === JobStatus.COMPLETED);
    return Math.round((successfulJobs.length / finishedJobs.length) * 100);
  }

  /**
   * Get empty stats object
   */
  private getEmptyStats(): QueueStats {
    return {
      totalJobs: 0,
      pendingJobs: 0,
      processingJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      pausedJobs: 0,
      activeJobs: 0,
      averageProgress: 0,
      averageProcessingTime: 0,
      successRate: 0,
      lastUpdated: new Date(),
    };
  }
}

// Singleton instance
let enhancedQueueInstance: EnhancedJobQueue | null = null;

export function getEnhancedJobQueue(): EnhancedJobQueue {
  if (!enhancedQueueInstance) {
    const maxConcurrent = parseInt(process.env.MAX_CONCURRENT_JOBS || '3');
    enhancedQueueInstance = new EnhancedJobQueue(maxConcurrent);
  }
  return enhancedQueueInstance;
}
