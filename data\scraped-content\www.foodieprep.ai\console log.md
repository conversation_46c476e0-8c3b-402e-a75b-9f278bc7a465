🔄 Processing pre-processed data from dashboard
📋 Extracted 1 URLs from processed data
✅ Data processing successful: 1 valid items, 0 invalid items
✅ Bulk job 98625b12-4eea-42e3-af7f-3ad6db235b72 created with 1 items
 POST /api/admin/bulk-processing 200 in 370ms
✅ Updated job 98625b12-4eea-42e3-af7f-3ad6db235b72 status: undefined → undefined (vundefined)
🔄 Processing 1 batches for job 98625b12-4eea-42e3-af7f-3ad6db235b72
📦 Processing batch 1/1 (1 items)
🔧 Ensuring 1 tools exist in database...
🔧 Ensuring 1 tools exist...
📊 API returning 1 bulk processing jobs with snake_case fields
 GET /api/admin/bulk-processing 200 in 877ms
✅ Created new tool: 76a9d820-7e50-4389-8806-0a32628aa313 for https://www.foodieprep.ai/
📦 Batch 1: 1 successful, 0 failed
✅ Tool creation completed: 1 tools ready
🎯 Creating job: tool_processing
📋 Status updated for job d5a9b86e-f563-4c2a-8b1d-798f22742c15: pending
🚀 Starting enhanced job processing loop
✅ Job d5a9b86e-f563-4c2a-8b1d-798f22742c15 (tool_processing) added to queue   
🔄 Processing job d5a9b86e-f563-4c2a-8b1d-798f22742c15 (tool_processing)
📊 Memory: 453MB/487MB, Resources: 0, Jobs: 0
📊 Memory usage: 458MB / 487MB (0 active jobs)
📋 Status updated for job d5a9b86e-f563-4c2a-8b1d-798f22742c15: processing
🔄 Processing existing tool 76a9d820-7e50-4389-8806-0a32628aa313 for https://www.foodieprep.ai/
🎯 Creating job: web_scraping
 ✓ Compiled /api/admin/bulk-processing/[id] in 343ms (1546 modules)
✅ Loading configuration from JSONB column
📋 Status updated for job 7f78f19d-70eb-4eff-9141-e8e2f907550c: pending
✅ Job 7f78f19d-70eb-4eff-9141-e8e2f907550c (web_scraping) added to queue      
🤖 Automated cleanup started (every 30 minutes)
📊 Cleanup monitoring started (every 15 minutes)
🔄 Processing job 7f78f19d-70eb-4eff-9141-e8e2f907550c (web_scraping)
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 1230ms
✅ Loading configuration from JSONB column
🔄 Processing job 7f78f19d-70eb-4eff-9141-e8e2f907550c (web_scraping)
📋 Status updated for job 7f78f19d-70eb-4eff-9141-e8e2f907550c: processing
🕷️ Enhanced web scraping job started for: https://www.foodieprep.ai/ (Tool ID:  76a9d820-7e50-4389-8806-0a32628aa313)
🚀 Starting enhanced scraping for: https://www.foodieprep.ai/
🎯 COST-OPTIMIZED SCRAPING: https://www.foodieprep.ai/
🔧 Request parameters:
   token: 8e7e405f...
   url: https://www.foodieprep.ai/
   device: desktop
   output: markdown
   timeout: 15000
🔗 Scraping URL: https://www.foodieprep.ai/
📡 Request URL: https://api.scrape.do/?token=8e7e405ff81145c4afe447610ddb9a7f785f494dddc&url=https%3A%2F%2Fwww.foodieprep.ai%2F&device=desktop&output=markdown&timeout=15000
📋 Status updated for job 7f78f19d-70eb-4eff-9141-e8e2f907550c: processing
🕷️ Enhanced web scraping job started for: https://www.foodieprep.ai/ (Tool ID:  76a9d820-7e50-4389-8806-0a32628aa313)
🚀 Starting enhanced scraping for: https://www.foodieprep.ai/
🎯 COST-OPTIMIZED SCRAPING: https://www.foodieprep.ai/
🔧 Request parameters:
   token: 8e7e405f...
   url: https://www.foodieprep.ai/
   device: desktop
   output: markdown
   timeout: 15000
🔗 Scraping URL: https://www.foodieprep.ai/
📡 Request URL: https://api.scrape.do/?token=8e7e405ff81145c4afe447610ddb9a7f785f494dddc&url=https%3A%2F%2Fwww.foodieprep.ai%2F&device=desktop&output=markdown&timeout=15000
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 296ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 290ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 297ms
✅ Loading configuration from JSONB column
✅ Scraping successful - 7073 characters received
💰 COST-SAVE: Using basic content despite quality issues (AI Quality Focus - Probability: 0.30, Expected Quality: 1.5x improvement)
💰 COST-OPTIMIZER: Using basic content as determined by cost analysis
🔍 Text analysis: 711 words, 136 sentences, length: 6975
Content analysis: Content sufficient (ratio: 0.99, confidence: 70) - KEEPING content
💰 MEDIA-OPTIMIZED: Extracting media from basic content without additional scraping
✅ Enhanced scraping completed in 6342ms
🔍 DEBUG: Attempting database storage...
🔍 DEBUG: Environment check passed
🔍 DEBUG: Supabase URL: https://gvcdqspryxrvxadfpwux.s...
🔍 DEBUG: Service key present: true
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 314ms
🔍 DEBUG: Database connection test passed
🔍 DEBUG: Insert data prepared: {
  url: 'https://www.foodieprep.ai/',
  contentLength: 7073,
  success: true,
  timestamp: '2025-06-24T15:44:18.765Z',
  credits_used: 1,
  metadataKeys: [
    'creditsUsed',
    'requestType',
    'proxyType',
    'browserEnabled',
    'remainingCredits',
    'resolvedUrl',
    'processingTime',
    'validation',
    'qualityScore'
  ]
}
✅ Database storage successful: {
  id: '9d5ab279-f0f4-4ac4-b969-f3a6916fe13d',
  url: 'https://www.foodieprep.ai/',
  content: '- Meta: charset: utf-8\n' +
    '- Meta: name: viewport, content: width=device-width, initial-scale=1\n' + 
    '- Meta: name: viewport, content: width=device-width, initial-scale=1.0\n' +
    '- Title: FoodiePrep - AI-Powered Personalised Recipes and Meal Planning\n' +
    '- Meta: name: description, content: Plan your meals and discover new recipes with FoodiePrep, your AI-powered culinary assistant\n' +
    '- Meta: name: keywords, content: meal planning,recipe generator,AI recipes,personalized meals,food planning,cooking assistant\n' +
    '- Meta: name: robots, content: index, follow\n' +
    '- Meta: property: og:title, content: FoodiePrep - AI-Powered Personalised Recipes and Meal Planning\n' +
    '- Meta: property: og:description, content: Plan your meals and discover new recipes with FoodiePrep, your AI-powered culinary assistant\n' +
    '- Meta: property: og:site_name, content: FoodiePrep\n' +
    '- Meta: property: og:locale, content: en_US\n' +
    '- Meta: content: https://www.foodieprep.ai/icon_1200x600.png, property: og:image\n' +
    '- Meta: property: og:image:width, content: 1200\n' +
    '- Meta: property: og:image:height, content: 600\n' +
    '- Meta: property: og:image:alt, content: FoodiePrep Logo\n' +
    '- Meta: property: og:type, content: website\n' +
    '- Meta: name: twitter:card, content: summary\n' +
    '- Meta: name: twitter:title, content: FoodiePrep - AI-Powered Personalised Recipes and Meal Planning\n' +
    '- Meta: name: twitter:description, content: Plan your meals and discover new recipes with FoodiePrep, your AI-powered culinary assistant\n' +
    '- Meta: name: twitter:image, content: https://www.foodieprep.ai/icon_512.png\n' +
    '- Meta: name: next-size-adjust\n' +
    '\n' +
    '[![FoodiePrep Logo](http://www.foodieprep.ai/icon_512.png)FoodiePrep](http://www.foodieprep.ai/) [Features](http://www.foodieprep.ai/#features) [How It Works](http://www.foodieprep.ai/#how-it-works) [Pricing](http://www.foodieprep.ai/#pricing) [Blog](http://www.foodieprep.ai/blog) [Recipes](http://www.foodieprep.ai/recipes) [Login / Sign Up](http://www.foodieprep.ai/auth)\n' +
    '\n' +
    '![Kitchen background](http://www.foodieprep.ai/hero.jpeg)\n' +
    '\n' +
    '# Recipes that Start with You\n' +
    '\n' +
    'Meal Planning Made Personal\n' +
    '\n' +
    '[Get Started for Free](http://www.foodieprep.ai/auth)\n' +
    '\n' +
    '[![Download on the App Store](http://www.foodieprep.ai/Download_on_the_App_Store_Badge_US-UK_RGB_blk_092917.svg)](https://apps.apple.com/app/foodieprep/id6745411191)[![Get it on Google Play](http://www.foodieprep.ai/google-play-badge-logo-svgrepo-com.svg)](https://play.google.com/store/apps/details?id=com.foodieprep.app&pcampaignid=web_share)\n' +
    '\n' +
    '## Tailored to Your Taste Buds\n' +
    '\n' +
    '![Spicy vegetarian recipe](http://www.foodieprep.ai/spicyvegetarianrecipe.jpeg)\n' +
    '\n' +
    '### Personalised Recipes\n' +
    '\n' +
    'Discover a world of flavors customised just for you.\n' +
    '\n' +
    '> "Create a spicy vegetarian recipe using ingredients in my fridge."\n' + 
    '\n' +
    '![Italian dinner idea recipe](http://www.foodieprep.ai/italiandinneridearecipe.jpeg)\n' +
    '\n' +
    '### Diverse Cuisine Options\n' +
    '\n' +
    'Explore dishes for every occasion, skill level, and cuisine type.\n' +    
    '\n' +
    '> "Italian dinner ideas for a romantic date night."\n' +
    '\n' +
    '![Indulgent healthy recipe](http://www.foodieprep.ai/induglenthealthyrecipe.jpeg)\n' +
    '\n' +
    '### Health-Conscious Choices\n' +
    '\n' +
    'Achieve your health and fitness goals without compromising on flavor.\n' +
    '\n' +
    '> "Generate high-protein, low-carb meal ideas for the week."\n' +
    '\n' +
    '## Meet Chef Foodie\n' +
    '\n' +
    'Your Personal Culinary AI Assistant\n' +
    '\n' +
    '### Your Intelligent Kitchen Companion\n' +
    '\n' +
    "Chef Foodie is more than just a recipe generator - it's your personal culinary expert. Ask questions, get cooking tips, and receive step-by-step guidance throughout your cooking journey.\n" +
    '\n' +
    '[Try Chef Foodie Now](http://www.foodieprep.ai/auth)\n' +
    '\n' +
    '![Chef Foodie Conversation](https://api.foodieprep.ai/storage/v1/object/public/Public-Images/ChefFoodie.png?t=2024-12-03T20%3A14%3A40.286Z)\n' +
    '\n' +
    '## End to End Meal Planning\n' +
    '\n' +
    '### AI-Powered Recipe Generation\n' +
    '\n' +
    'Create personalised recipes based on your preferences and dietary needs.\n' +
    '\n' +
    '### Recipe Import\n' +
    '\n' +
    'Import your favourite recipes from anywhere on the web, via text or YouTube. Keep all your recipes in one place.\n' +
    '\n' +
    '### Recipe Management\n' +
    '\n' +
    'Organise your recipes into customised recipe books for easy access and meal planning.\n' +
    '\n' +
    '### Meal Planner\n' +
    '\n' +
    'Plan your meals effortlessly with our intuitive meal planning tool.\n' +  
    '\n' +
    '### Smart Shopping Lists\n' +
    '\n' +
    'Generate shopping lists from your meal plans and find ingredients at your preferred local grocery stores.\n' +
    '\n' +
    '### Ingredient Management\n' +
    '\n' +
    'Give your perishable ingredients a new purpose. Or simply choose which ingredients to include or exclude in your recipes.\n' +
    '\n' +
    '## Unlock Infinite Culinary Possibilities\n' +
    '\n' +
    '## Know What You Eat\n' +
    '\n' +
    'Comprehensive nutritional information\n' +
    '\n' +
    '![Nutrition Information](http://www.foodieprep.ai/NutritionDetails.png)\n' +
    '\n' +
    '## How It Works\n' +
    '\n' +
    '1\n' +
    '\n' +
    '### Stock Your Virtual Pantry\n' +
    '\n' +
    'Input ingredients you have on hand or prefer to cook with.\n' +
    '\n' +
    '2\n' +
    '\n' +
    '### Discover AI-Powered Recipes\n' +
    '\n' +
    'Get personalised recipe ideas based on your preferences and dietary needs.\n' +
    '\n' +
    '3\n' +
    '\n' +
    '### Import Your Favourites\n' +
    '\n' +
    'Bring your favourite recipes from around the web into FoodiePrep.\n' +    
    '\n' +
    '4\n' +
    '\n' +
    '### Curate Your Cookbooks\n' +
    '\n' +
    'Save and categorise your favourite recipes for easy access.\n' +
    '\n' +
    '5\n' +
    '\n' +
    '### Plan your meals\n' +
    '\n' +
    'Schedule your meals in advance with the meal planner.\n' +
    '\n' +
    '6\n' +
    '\n' +
    '### Shop Smarter\n' +
    '\n' +
    'Generate a smart shopping list based on your meal plan.\n' +
    '\n' +
    '7\n' +
    '\n' +
    '### Share Your Creations\n' +
    '\n' +
    'Share your favourite recipes with family, friends, other FoodiePrep users, or the world.\n' +
    '\n' +
    '8\n' +
    '\n' +
    '### Cook with Confidence\n' +
    '\n' +
    'Follow step-by-step instructions and enjoy your culinary creations!\n' +  
    '\n' +
    '## Why FoodiePrep\n' +
    '\n' +
    '### Save Time\n' +
    '\n' +
    'Streamline your meal planning and grocery shopping process.\n' +
    '\n' +
    '### Eat Healthier\n' +
    '\n' +
    'Discover nutritious recipes tailored to your dietary needs.\n' +
    '\n' +
    '### Reduce Food Waste\n' +
    '\n' +
    'Plan meals efficiently and use ingredients wisely.\n' +
    '\n' +
    '### Enjoy Variety\n' +
    '\n' +
    'Explore new cuisines and never get bored with your meals.\n' +
    '\n' +
    '## What Our Users Say\n' +
    '\n' +
    `"FoodiePrep has revolutionised my meal planning. The AI-generated recipes are always spot-on and have introduced me to new flavors I never knew I'd love!"\n` +
    '\n' +
    'Sarah J.\n' +
    '\n' +
    'Busy Mom of Three\n' +
    '\n' +
    `"As a fitness enthusiast, I love how FoodiePrep tailors recipes to my macros. It's made eating healthy not just easy, but genuinely enjoyable."\n` +     
    '\n' +
    'Mike C.\n' +
    '\n' +
    'Personal Trainer\n' +
    '\n' +
    `"The shopping list feature is a game-changer. I've cut my grocery bills and virtually eliminated food waste."\n` +
    '\n' +
    'Emma R.\n' +
    '\n' +
    'Budget-Conscious Foodie\n' +
    '\n' +
    '![Meal Planning Background](http://www.foodieprep.ai/mealplanning.jpeg)\n' +
    '\n' +
    '## Ready to Transform Your Meal Planning?\n' +
    '\n' +
    'Make every meal a delightful experience with FoodiePrep.\n' +
    '\n' +
    '[Start Cooking!](http://www.foodieprep.ai/auth)\n' +
    '\n' +
    '## Get Started Today\n' +
    '\n' +
    '## Frequently Asked Questions\n' +
    '\n' +
    "### How does FoodiePrep's AI recipe generation work?\n" +
    '\n' +
    '### Can I use FoodiePrep if I have dietary restrictions or allergies?\n' +
    '\n' +
    '### Can I share Recipes?\n' +
    '\n' +
    '### Is there a mobile app available for FoodiePrep?\n' +
    '\n' +
    '© 2024 FoodiePrep Ltd. All rights reserved.\n' +
    '\n' +
    '[Terms of Service](http://www.foodieprep.ai/terms-and-conditions) [Privacy Policy](http://www.foodieprep.ai/privacy-policy) [Contact Us](http://www.foodieprep.ai/public-contact) [Follow us on Instagram](https://www.instagram.com/foodieprep.ai/) [Follow us on Facebook](https://www.facebook.com/profile.php?id=61567458549135)',
  success: true,
  timestamp: '2025-06-24T15:44:18.765+00:00',
  credits_used: 1,
  metadata: {
    proxyType: 'datacenter',
    validation: false,
    creditsUsed: 1,
    requestType: 'Datacenter Proxy',
    resolvedUrl: 'https://www.foodieprep.ai/',
    qualityScore: 65,
    browserEnabled: false,
    processingTime: 6342,
    remainingCredits: 559
  },
  content_length: 7073,
  domain: 'www.foodieprep.ai',
  created_at: '2025-06-24T15:44:20.912431+00:00',
  updated_at: '2025-06-24T15:44:20.912431+00:00'
}
✅ Database storage successful
📁 Content stored successfully: G:\projects\dudeai1\data\scraped-content\www.foodieprep.ai\www.foodieprep.ai__2025-06-24T15-44-19-419Z.md
📁 Content stored: G:\projects\dudeai1\data\scraped-content\www.foodieprep.ai\www.foodieprep.ai__2025-06-24T15-44-19-419Z.md
✅ Enhanced web scraping completed for: https://www.foodieprep.ai/ (1 credits) 
✅ Scraping successful - 7073 characters received
💰 COST-SAVE: Using basic content despite quality issues (AI Quality Focus - Probability: 0.30, Expected Quality: 1.5x improvement)
💰 COST-OPTIMIZER: Using basic content as determined by cost analysis
🔍 Text analysis: 711 words, 136 sentences, length: 6975
Content analysis: Content sufficient (ratio: 0.99, confidence: 70) - KEEPING content
💰 MEDIA-OPTIMIZED: Extracting media from basic content without additional scraping
✅ Enhanced scraping completed in 7089ms
🔍 DEBUG: Attempting database storage...
🔍 DEBUG: Environment check passed
🔍 DEBUG: Supabase URL: https://gvcdqspryxrvxadfpwux.s...
🔍 DEBUG: Service key present: true
✅ Loading configuration from JSONB column
 ✓ Compiled in 212ms (555 modules)
 GET /admin/bulk 200 in 187ms
 ✓ Compiled in 224ms (555 modules)
 GET /admin/bulk 200 in 21ms
📊 Progress updated for job 7f78f19d-70eb-4eff-9141-e8e2f907550c: 100% - Job completed successfully
✅ Job 7f78f19d-70eb-4eff-9141-e8e2f907550c completed successfully
🔍 DEBUG: Database connection test passed
🔍 DEBUG: Insert data prepared: {
  url: 'https://www.foodieprep.ai/',
  contentLength: 7073,
  success: true,
  timestamp: '2025-06-24T15:44:19.595Z',
  credits_used: 1,
  metadataKeys: [
    'creditsUsed',
    'requestType',
    'proxyType',
    'browserEnabled',
    'remainingCredits',
    'resolvedUrl',
    'processingTime',
    'validation',
    'qualityScore'
  ]
}
🎯 Creating job: content_generation
✅ Database storage successful: {
  id: 'ea9dd714-c759-49d6-9345-db7b671c36c0',
  url: 'https://www.foodieprep.ai/',
  content: '- Meta: charset: utf-8\n' +
    '- Meta: name: viewport, content: width=device-width, initial-scale=1\n' + 
    '- Meta: name: viewport, content: width=device-width, initial-scale=1.0\n' +
    '- Title: FoodiePrep - AI-Powered Personalised Recipes and Meal Planning\n' +
    '- Meta: name: description, content: Plan your meals and discover new recipes with FoodiePrep, your AI-powered culinary assistant\n' +
    '- Meta: name: keywords, content: meal planning,recipe generator,AI recipes,personalized meals,food planning,cooking assistant\n' +
    '- Meta: name: robots, content: index, follow\n' +
    '- Meta: property: og:title, content: FoodiePrep - AI-Powered Personalised Recipes and Meal Planning\n' +
    '- Meta: property: og:description, content: Plan your meals and discover new recipes with FoodiePrep, your AI-powered culinary assistant\n' +
    '- Meta: property: og:site_name, content: FoodiePrep\n' +
    '- Meta: property: og:locale, content: en_US\n' +
    '- Meta: property: og:image, content: https://www.foodieprep.ai/icon_1200x600.png\n' +
    '- Meta: property: og:image:width, content: 1200\n' +
    '- Meta: property: og:image:height, content: 600\n' +
    '- Meta: property: og:image:alt, content: FoodiePrep Logo\n' +
    '- Meta: property: og:type, content: website\n' +
    '- Meta: content: summary, name: twitter:card\n' +
    '- Meta: name: twitter:title, content: FoodiePrep - AI-Powered Personalised Recipes and Meal Planning\n' +
    '- Meta: name: twitter:description, content: Plan your meals and discover new recipes with FoodiePrep, your AI-powered culinary assistant\n' +
    '- Meta: name: twitter:image, content: https://www.foodieprep.ai/icon_512.png\n' +
    '- Meta: name: next-size-adjust\n' +
    '\n' +
    '[![FoodiePrep Logo](http://www.foodieprep.ai/icon_512.png)FoodiePrep](http://www.foodieprep.ai/) [Features](http://www.foodieprep.ai/#features) [How It Works](http://www.foodieprep.ai/#how-it-works) [Pricing](http://www.foodieprep.ai/#pricing) [Blog](http://www.foodieprep.ai/blog) [Recipes](http://www.foodieprep.ai/recipes) [Login / Sign Up](http://www.foodieprep.ai/auth)\n' +
    '\n' +
    '![Kitchen background](http://www.foodieprep.ai/hero.jpeg)\n' +
    '\n' +
    '# Recipes that Start with You\n' +
    '\n' +
    'Meal Planning Made Personal\n' +
    '\n' +
    '[Get Started for Free](http://www.foodieprep.ai/auth)\n' +
    '\n' +
    '[![Download on the App Store](http://www.foodieprep.ai/Download_on_the_App_Store_Badge_US-UK_RGB_blk_092917.svg)](https://apps.apple.com/app/foodieprep/id6745411191)[![Get it on Google Play](http://www.foodieprep.ai/google-play-badge-logo-svgrepo-com.svg)](https://play.google.com/store/apps/details?id=com.foodieprep.app&pcampaignid=web_share)\n' +
    '\n' +
    '## Tailored to Your Taste Buds\n' +
    '\n' +
    '![Spicy vegetarian recipe](http://www.foodieprep.ai/spicyvegetarianrecipe.jpeg)\n' +
    '\n' +
    '### Personalised Recipes\n' +
    '\n' +
    'Discover a world of flavors customised just for you.\n' +
    '\n' +
    '> "Create a spicy vegetarian recipe using ingredients in my fridge."\n' + 
    '\n' +
    '![Italian dinner idea recipe](http://www.foodieprep.ai/italiandinneridearecipe.jpeg)\n' +
    '\n' +
    '### Diverse Cuisine Options\n' +
    '\n' +
    'Explore dishes for every occasion, skill level, and cuisine type.\n' +    
    '\n' +
    '> "Italian dinner ideas for a romantic date night."\n' +
    '\n' +
    '![Indulgent healthy recipe](http://www.foodieprep.ai/induglenthealthyrecipe.jpeg)\n' +
    '\n' +
    '### Health-Conscious Choices\n' +
    '\n' +
    'Achieve your health and fitness goals without compromising on flavor.\n' +
    '\n' +
    '> "Generate high-protein, low-carb meal ideas for the week."\n' +
    '\n' +
    '## Meet Chef Foodie\n' +
    '\n' +
    'Your Personal Culinary AI Assistant\n' +
    '\n' +
    '### Your Intelligent Kitchen Companion\n' +
    '\n' +
    "Chef Foodie is more than just a recipe generator - it's your personal culinary expert. Ask questions, get cooking tips, and receive step-by-step guidance throughout your cooking journey.\n" +
    '\n' +
    '[Try Chef Foodie Now](http://www.foodieprep.ai/auth)\n' +
    '\n' +
    '![Chef Foodie Conversation](https://api.foodieprep.ai/storage/v1/object/public/Public-Images/ChefFoodie.png?t=2024-12-03T20%3A14%3A40.286Z)\n' +
    '\n' +
    '## End to End Meal Planning\n' +
    '\n' +
    '### AI-Powered Recipe Generation\n' +
    '\n' +
    'Create personalised recipes based on your preferences and dietary needs.\n' +
    '\n' +
    '### Recipe Import\n' +
    '\n' +
    'Import your favourite recipes from anywhere on the web, via text or YouTube. Keep all your recipes in one place.\n' +
    '\n' +
    '### Recipe Management\n' +
    '\n' +
    'Organise your recipes into customised recipe books for easy access and meal planning.\n' +
    '\n' +
    '### Meal Planner\n' +
    '\n' +
    'Plan your meals effortlessly with our intuitive meal planning tool.\n' +  
    '\n' +
    '### Smart Shopping Lists\n' +
    '\n' +
    'Generate shopping lists from your meal plans and find ingredients at your preferred local grocery stores.\n' +
    '\n' +
    '### Ingredient Management\n' +
    '\n' +
    'Give your perishable ingredients a new purpose. Or simply choose which ingredients to include or exclude in your recipes.\n' +
    '\n' +
    '## Unlock Infinite Culinary Possibilities\n' +
    '\n' +
    '## Know What You Eat\n' +
    '\n' +
    'Comprehensive nutritional information\n' +
    '\n' +
    '![Nutrition Information](http://www.foodieprep.ai/NutritionDetails.png)\n' +
    '\n' +
    '## How It Works\n' +
    '\n' +
    '1\n' +
    '\n' +
    '### Stock Your Virtual Pantry\n' +
    '\n' +
    'Input ingredients you have on hand or prefer to cook with.\n' +
    '\n' +
    '2\n' +
    '\n' +
    '### Discover AI-Powered Recipes\n' +
    '\n' +
    'Get personalised recipe ideas based on your preferences and dietary needs.\n' +
    '\n' +
    '3\n' +
    '\n' +
    '### Import Your Favourites\n' +
    '\n' +
    'Bring your favourite recipes from around the web into FoodiePrep.\n' +    
    '\n' +
    '4\n' +
    '\n' +
    '### Curate Your Cookbooks\n' +
    '\n' +
    'Save and categorise your favourite recipes for easy access.\n' +
    '\n' +
    '5\n' +
    '\n' +
    '### Plan your meals\n' +
    '\n' +
    'Schedule your meals in advance with the meal planner.\n' +
    '\n' +
    '6\n' +
    '\n' +
    '### Shop Smarter\n' +
    '\n' +
    'Generate a smart shopping list based on your meal plan.\n' +
    '\n' +
    '7\n' +
    '\n' +
    '### Share Your Creations\n' +
    '\n' +
    'Share your favourite recipes with family, friends, other FoodiePrep users, or the world.\n' +
    '\n' +
    '8\n' +
    '\n' +
    '### Cook with Confidence\n' +
    '\n' +
    'Follow step-by-step instructions and enjoy your culinary creations!\n' +  
    '\n' +
    '## Why FoodiePrep\n' +
    '\n' +
    '### Save Time\n' +
    '\n' +
    'Streamline your meal planning and grocery shopping process.\n' +
    '\n' +
    '### Eat Healthier\n' +
    '\n' +
    'Discover nutritious recipes tailored to your dietary needs.\n' +
    '\n' +
    '### Reduce Food Waste\n' +
    '\n' +
    'Plan meals efficiently and use ingredients wisely.\n' +
    '\n' +
    '### Enjoy Variety\n' +
    '\n' +
    'Explore new cuisines and never get bored with your meals.\n' +
    '\n' +
    '## What Our Users Say\n' +
    '\n' +
    `"FoodiePrep has revolutionised my meal planning. The AI-generated recipes are always spot-on and have introduced me to new flavors I never knew I'd love!"\n` +
    '\n' +
    'Sarah J.\n' +
    '\n' +
    'Busy Mom of Three\n' +
    '\n' +
    `"As a fitness enthusiast, I love how FoodiePrep tailors recipes to my macros. It's made eating healthy not just easy, but genuinely enjoyable."\n` +     
    '\n' +
    'Mike C.\n' +
    '\n' +
    'Personal Trainer\n' +
    '\n' +
    `"The shopping list feature is a game-changer. I've cut my grocery bills and virtually eliminated food waste."\n` +
    '\n' +
    'Emma R.\n' +
    '\n' +
    'Budget-Conscious Foodie\n' +
    '\n' +
    '![Meal Planning Background](http://www.foodieprep.ai/mealplanning.jpeg)\n' +
    '\n' +
    '## Ready to Transform Your Meal Planning?\n' +
    '\n' +
    'Make every meal a delightful experience with FoodiePrep.\n' +
    '\n' +
    '[Start Cooking!](http://www.foodieprep.ai/auth)\n' +
    '\n' +
    '## Get Started Today\n' +
    '\n' +
    '## Frequently Asked Questions\n' +
    '\n' +
    "### How does FoodiePrep's AI recipe generation work?\n" +
    '\n' +
    '### Can I use FoodiePrep if I have dietary restrictions or allergies?\n' +
    '\n' +
    '### Can I share Recipes?\n' +
    '\n' +
    '### Is there a mobile app available for FoodiePrep?\n' +
    '\n' +
    '© 2024 FoodiePrep Ltd. All rights reserved.\n' +
    '\n' +
    '[Terms of Service](http://www.foodieprep.ai/terms-and-conditions) [Privacy Policy](http://www.foodieprep.ai/privacy-policy) [Contact Us](http://www.foodieprep.ai/public-contact) [Follow us on Instagram](https://www.instagram.com/foodieprep.ai/) [Follow us on Facebook](https://www.facebook.com/profile.php?id=61567458549135)',
  success: true,
  timestamp: '2025-06-24T15:44:19.595+00:00',
  credits_used: 1,
  metadata: {
    proxyType: 'datacenter',
    validation: false,
    creditsUsed: 1,
    requestType: 'Datacenter Proxy',
    resolvedUrl: 'https://www.foodieprep.ai/',
    qualityScore: 65,
    browserEnabled: false,
    processingTime: 7089,
    remainingCredits: 559
  },
  content_length: 7073,
  domain: 'www.foodieprep.ai',
  created_at: '2025-06-24T15:44:22.271916+00:00',
  updated_at: '2025-06-24T15:44:22.271916+00:00'
}
✅ Database storage successful
📁 Content stored successfully: G:\projects\dudeai1\data\scraped-content\www.foodieprep.ai\www.foodieprep.ai__2025-06-24T15-44-20-774Z.md
📁 Content stored: G:\projects\dudeai1\data\scraped-content\www.foodieprep.ai\www.foodieprep.ai__2025-06-24T15-44-20-774Z.md
✅ Enhanced web scraping completed for: https://www.foodieprep.ai/ (1 credits) 
 ✓ Compiled in 178ms (555 modules)
 GET /admin/bulk 200 in 11ms
📋 Status updated for job d5825fa6-f3b3-495f-8434-7b175a23570a: pending
✅ Job d5825fa6-f3b3-495f-8434-7b175a23570a (content_generation) added to queue
🔄 Processing job d5825fa6-f3b3-495f-8434-7b175a23570a (content_generation)
📊 Progress updated for job 7f78f19d-70eb-4eff-9141-e8e2f907550c: 100% - Job completed successfully
✅ Job 7f78f19d-70eb-4eff-9141-e8e2f907550c completed successfully
📋 Status updated for job d5825fa6-f3b3-495f-8434-7b175a23570a: processing
Starting content generation pipeline for: https://www.foodieprep.ai/
Tool ID: 76a9d820-7e50-4389-8806-0a32628aa313
Content size: 7150 characters
Options: {
  skipValidation: false,
  requireEditorialReview: false,
  autoApprove: false,
  qualityThreshold: 80,
  priority: 'normal'
}
Starting content generation pipeline for tool: 76a9d820-7e50-4389-8806-0a32628aa313
[2025-06-24T15:44:23.012Z] INFO: Using AI Dude methodology with gpt-4o-2024-11-20 | component=ai-system operation=ai-dude-generation toolUrl=https://www.foodieprep.ai/ model=gpt-4o-2024-11-20 provider=openai methodology=ai_dude
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 290ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 289ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 318ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 288ms
✅ Loading configuration from JSONB column
📊 Memory: 507MB/580MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 507MB / 580MB (1 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 296ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 292ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 300ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/98625b12-4eea-42e3-af7f-3ad6db235b72 200 in 301ms
📊 Relevance Score Breakdown:
      Concept Overlap: 0.18
      Domain Relevance: 0.90
      Structural Similarity: 0.33
      Final Score: 0.44
✅ Loading configuration from JSONB column
Pipeline completed successfully for tool: 76a9d820-7e50-4389-8806-0a32628aa313
Status: pending_manual_review, Workflow: manual_review
Quality Score: 94