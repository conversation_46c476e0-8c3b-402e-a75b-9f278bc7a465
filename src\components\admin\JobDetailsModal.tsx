'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Job, JobStatus } from '@/lib/jobs/types';

interface JobDetailsModalProps {
  job: Job;
  onClose: () => void;
  onJobAction: (action: 'pause' | 'resume' | 'stop' | 'retry' | 'delete', jobId: string) => void;
}

interface TabConfig {
  id: string;
  label: string;
  icon: string;
}

/**
 * Job Details Modal Component
 * 
 * Displays comprehensive information about a specific job.
 * Features:
 * - Tabbed interface for different data views
 * - Real-time job status and progress
 * - Detailed job data and error information
 * - Action buttons for job control
 * - JSON viewer for complex data structures
 */
export function JobDetailsModal({ 
  job, 
  onClose, 
  onJobAction 
}: JobDetailsModalProps): React.JSX.Element {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs: TabConfig[] = [
    { id: 'overview', label: 'Overview', icon: '📋' },
    { id: 'data', label: 'Job Data', icon: '📊' },
    { id: 'progress', label: 'Progress', icon: '📈' },
    { id: 'logs', label: 'Logs', icon: '📝' },
    { id: 'error', label: 'Error Details', icon: '❌' },
  ];

  // Format date for display
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleString();
  };

  // Format duration
  const formatDuration = (start: Date | string, end?: Date | string) => {
    const startTime = new Date(start).getTime();
    const endTime = end ? new Date(end).getTime() : Date.now();
    const duration = endTime - startTime;
    
    if (duration < 60000) return `${Math.round(duration / 1000)}s`;
    if (duration < 3600000) return `${Math.round(duration / 60000)}m`;
    return `${Math.round(duration / 3600000)}h`;
  };

  // Get available actions based on job status (consistent with JobListTable)
  const getAvailableActions = (status: JobStatus) => {
    switch (status) {
      case JobStatus.PROCESSING:
        return ['pause', 'stop'];
      case JobStatus.PAUSED:
        return ['resume', 'stop'];
      case JobStatus.FAILED:
        return ['retry', 'delete'];
      case JobStatus.RETRYING:
        return ['retry', 'delete'];
      case JobStatus.COMPLETED:
        return ['delete'];
      case JobStatus.PENDING:
        return ['stop', 'delete'];
      case JobStatus.STOPPED:
        // Stopped jobs can only be retried or deleted (NOT resumed)
        return ['retry', 'delete'];
      case JobStatus.CANCELLED:
        // Cancelled jobs can be retried or deleted
        return ['retry', 'delete'];
      case JobStatus.STOPPING:
        // Jobs that are stopping can only be deleted
        return ['delete'];
      default:
        return ['delete'];
    }
  };

  const availableActions = getAvailableActions(job.status);

  const actionLabels = {
    pause: '⏸️ Pause Job',
    resume: '▶️ Resume Job',
    stop: '⏹️ Stop Job',
    retry: '🔄 Retry Job',
    delete: '🗑️ Delete Job'
  };

  const actionColors = {
    pause: 'bg-yellow-600 hover:bg-yellow-700',
    resume: 'bg-green-600 hover:bg-green-700',
    stop: 'bg-red-600 hover:bg-red-700',
    retry: 'bg-blue-600 hover:bg-blue-700',
    delete: 'bg-red-600 hover:bg-red-700'
  };

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-400">Job ID</label>
                  <p className="text-white font-mono text-sm">{job.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-400">Type</label>
                  <p className="text-white">{job.type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-400">Status</label>
                  <p className="text-white">{job.status.charAt(0).toUpperCase() + job.status.slice(1)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-400">Priority</label>
                  <p className="text-white">{job.priority}</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-400">Created</label>
                  <p className="text-white">{formatDate(job.createdAt)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-400">Updated</label>
                  <p className="text-white">{formatDate(job.updatedAt)}</p>
                </div>
                {job.completedAt && (
                  <div>
                    <label className="text-sm font-medium text-gray-400">Completed</label>
                    <p className="text-white">{formatDate(job.completedAt)}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-gray-400">Duration</label>
                  <p className="text-white">{formatDuration(job.createdAt, job.completedAt)}</p>
                </div>
              </div>
            </div>

            {/* Progress */}
            <div>
              <label className="text-sm font-medium text-gray-400 mb-2 block">Progress</label>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Progress</span>
                  <span className="text-white">{job.progress || 0}%</span>
                </div>
                <div className="w-full bg-zinc-700 rounded-full h-3">
                  <div 
                    className="bg-orange-500 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${job.progress || 0}%` }}
                  />
                </div>
              </div>
            </div>

            {/* Attempts */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-400">Attempts</label>
                <p className="text-white">{job.attempts} / {job.maxAttempts}</p>
              </div>
              {job.toolId && (
                <div>
                  <label className="text-sm font-medium text-gray-400">Tool ID</label>
                  <p className="text-white font-mono text-sm">{job.toolId}</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'data':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-400 mb-2 block">Job Data</label>
              <pre className="bg-zinc-900 p-4 rounded-lg text-sm text-gray-300 overflow-auto max-h-96">
                {JSON.stringify(job.data, null, 2)}
              </pre>
            </div>
            {job.result && (
              <div>
                <label className="text-sm font-medium text-gray-400 mb-2 block">Result</label>
                <pre className="bg-zinc-900 p-4 rounded-lg text-sm text-gray-300 overflow-auto max-h-96">
                  {JSON.stringify(job.result, null, 2)}
                </pre>
              </div>
            )}
          </div>
        );

      case 'progress':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-400 mb-2 block">Progress Details</label>
              {job.progressDetails ? (
                <pre className="bg-zinc-900 p-4 rounded-lg text-sm text-gray-300 overflow-auto max-h-96">
                  {JSON.stringify(job.progressDetails, null, 2)}
                </pre>
              ) : (
                <p className="text-gray-400 italic">No detailed progress information available</p>
              )}
            </div>
            
            {/* Progress Timeline */}
            <div>
              <label className="text-sm font-medium text-gray-400 mb-2 block">Timeline</label>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-300">Created: {formatDate(job.createdAt)}</span>
                </div>
                {job.status !== JobStatus.PENDING && (
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                    <span className="text-sm text-gray-300">Started: {formatDate(job.updatedAt)}</span>
                  </div>
                )}
                {job.completedAt && (
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-300">Completed: {formatDate(job.completedAt)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'logs':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-400 mb-2 block">Job Logs</label>
              <div className="bg-zinc-900 p-4 rounded-lg text-sm text-gray-300 max-h-96 overflow-auto">
                <p className="text-gray-400 italic">Log viewing functionality will be implemented in a future update.</p>
                <p className="text-gray-500 text-xs mt-2">
                  This will show real-time logs from job execution, including debug information and step-by-step progress.
                </p>
              </div>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="space-y-4">
            {job.error ? (
              <div>
                <label className="text-sm font-medium text-gray-400 mb-2 block">Error Message</label>
                <div className="bg-red-900/20 border border-red-500/30 p-4 rounded-lg">
                  <p className="text-red-300 font-mono text-sm">{job.error}</p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-4xl mb-2">✅</div>
                <p className="text-gray-400">No errors reported for this job</p>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="bg-zinc-800 border border-zinc-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-zinc-700">
          <div>
            <h2 className="text-xl font-semibold text-white">Job Details</h2>
            <p className="text-sm text-gray-400 font-mono">{job.id}</p>
          </div>
          <div className="flex items-center space-x-3">
            {/* Action Buttons */}
            {availableActions.map(action => (
              <Button
                key={action}
                variant="primary"
                size="sm"
                onClick={() => onJobAction(action as any, job.id)}
                className={actionColors[action as keyof typeof actionColors]}
              >
                {actionLabels[action as keyof typeof actionLabels]}
              </Button>
            ))}
            <Button variant="outline" onClick={onClose}>
              ✕ Close
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-zinc-700">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-orange-400 border-b-2 border-orange-400 bg-zinc-700/50'
                  : 'text-gray-400 hover:text-white hover:bg-zinc-700/30'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {renderTabContent()}
        </div>
      </Card>
    </div>
  );
}

export default JobDetailsModal;
