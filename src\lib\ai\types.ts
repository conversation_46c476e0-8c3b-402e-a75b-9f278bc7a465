// AI Provider Types and Interfaces

export type AIProvider = 'openai' | 'openrouter';

export type AIModel = 
  | 'gpt-4o' 
  | 'gpt-4o-2024-11-20' 
  | 'gpt-4o-mini'
  | 'google/gemini-2.5-pro-preview'
  | 'google/gemini-pro-1.5'
  | 'anthropic/claude-3.5-sonnet';

export type ContentComplexity = 'simple' | 'medium' | 'complex';
export type ProcessingPriority = 'speed' | 'quality' | 'cost';

export interface AIModelConfig {
  provider: AIProvider;
  model: AIModel;
  maxInputTokens: number;
  maxOutputTokens: number;
  temperature: number;
  timeout: number;
  retryAttempts: number;
  structuredOutput: boolean;
  multiPageSupport: boolean;
  extraHeaders?: Record<string, string>;
}

export interface ModelSelectionCriteria {
  contentSize: number; // Token count of input content
  complexity: ContentComplexity;
  priority: ProcessingPriority;
  features: string[]; // Required features (caching, multimodal, etc.)
  scrapingCost?: number; // Cost of scraping operation
  contentQuality?: number; // Quality score of scraped content
}

export interface ModelConfig {
  provider: AIProvider;
  model: AIModel;
  maxTokens: number;
  reasoning: string;
}

export interface GenerationOptions {
  complexity?: ContentComplexity;
  priority?: ProcessingPriority;
  features?: string[];
  scrapingCost?: number;
  contentQuality?: number;
  maxRetries?: number;
  aiProvider?: 'openai' | 'openrouter'; // User's provider choice
}

export interface AIResponse {
  content: string;
  tokenUsage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;
  finishReason: string;
}

export interface GeneratedContent {
  detailed_description: string;
  features: string[];
  pricing: {
    type: 'Free' | 'Paid' | 'Freemium' | 'Open Source';
    plans?: Array<{
      name: string;
      price: string;
      features: string[];
    }>;
  };
  pros_and_cons: {
    pros: string[];
    cons: string[];
  };
  haiku?: {
    lines: [string, string, string];
    theme: string;
  };
  hashtags?: string[];
  social_links?: {
    twitter?: string | null;
    linkedin?: string | null;
    github?: string | null;
  };
  tokenUsage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface ValidationResult {
  isValid: boolean;
  issues: string[];
  score: number; // Quality score 0-100
  methodology?: string; // AI Dude methodology identifier
}

export interface GenerationResult {
  success: boolean;
  content?: GeneratedContent;
  validation?: ValidationResult;
  modelUsed?: ModelConfig;
  tokenUsage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  timestamp: string;
  error?: string;
  strategyUsed?: number; // Which fallback strategy was used
  methodology?: string; // AI Dude methodology identifier
  sectionType?: string; // For partial generation
}

export interface ErrorContext {
  attempt?: number;
  maxRetries?: number;
  provider?: AIProvider;
  model?: AIModel;
  operation?: string;
}

export interface ErrorResult {
  success: false;
  error: string;
  retryable: boolean;
  retryAfter?: number; // Seconds to wait before retry
  suggestion?: string;
  context: ErrorContext;
  timestamp: string;
}

export interface MultiPromptContext {
  isFirstChunk: boolean;
  isLastChunk: boolean;
  chunkIndex: number;
  totalChunks: number;
  accumulatedContext: string;
  toolUrl: string;
}

export interface AIProcessingConfig {
  temperature: number;
  maxTokens: number;
  priority: 'high' | 'normal' | 'low';
  qualityThreshold: 'high' | 'medium' | 'low';
  enhancedProcessing: boolean;
  multiPassValidation: boolean;
}

export interface ProcessedContent {
  optimizedContent: string;
  processingConfig: AIProcessingConfig;
  estimatedTokens: number;
  costOptimizationApplied: boolean;
}

// Model-specific output token limits (verified from provider documentation)
export const MODEL_OUTPUT_LIMITS: Record<string, number> = {
  // Google Models via OpenRouter (verified from Google Cloud docs)
  'google/gemini-2.5-pro-preview': 65536,       // 65K output tokens (confirmed)
  'google/gemini-pro-1.5': 8192,                // 8K output tokens

  // OpenAI Models (verified from OpenRouter)
  'openai/gpt-4o': 16384,                       // 16K output tokens
  'openai/gpt-4o-2024-11-20': 16384,            // 16K output tokens (confirmed)
  'openai/gpt-4o-mini': 16384,                  // 16K output tokens

  // Anthropic Models
  'anthropic/claude-3.5-sonnet': 8192,          // 8K output tokens

  // Other Models
  'meta-llama/llama-3.1-405b': 4096             // 4K output tokens
};

// Default configurations for each provider
export const OPENAI_CONFIG: AIModelConfig = {
  provider: 'openai',
  model: 'gpt-4o-2024-11-20',
  maxInputTokens: 128000, // GPT-4o input limit
  maxOutputTokens: 16384, // GPT-4o output limit
  temperature: 0.7,
  timeout: 60000,
  retryAttempts: 3,
  structuredOutput: true,
  multiPageSupport: true
};

export const OPENROUTER_CONFIG: AIModelConfig = {
  provider: 'openrouter',
  model: 'google/gemini-2.5-pro-preview',
  maxInputTokens: 1048576, // ~1M tokens input context
  maxOutputTokens: 65536, // Gemini 2.5 Pro Preview output limit (corrected)
  temperature: 0.7,
  timeout: 120000,
  retryAttempts: 3,
  structuredOutput: true,
  multiPageSupport: true,
  extraHeaders: {
    'HTTP-Referer': process.env.SITE_URL || 'https://aidude.com',
    'X-Title': 'AI Dude Directory'
  }
};
