✅ Bulk job 7383e739-e6f4-4a77-9c51-2cdc825a23b7 created with 1 items
 POST /api/admin/bulk-processing 200 in 369ms
✅ Updated job 7383e739-e6f4-4a77-9c51-2cdc825a23b7 status: undefined → undefined (vundefined)
🔄 Processing 1 batches for job 7383e739-e6f4-4a77-9c51-2cdc825a23b7
📦 Processing batch 1/1 (1 items)
🔧 Ensuring 1 tools exist in database...
🔧 Ensuring 1 tools exist...
📊 API returning 2 bulk processing jobs with snake_case fields
 GET /api/admin/bulk-processing 200 in 334ms
✅ Created new tool: 55f05e79-8477-437f-a921-13df6316a4e0 for https://www.foodieprep.ai/
📦 Batch 1: 1 successful, 0 failed
✅ Tool creation completed: 1 tools ready
🎯 Creating job: tool_processing
📋 Status updated for job 3dd8e24f-d5bb-4d3c-8b50-4614fb4f340b: pending
🚀 Starting enhanced job processing loop
✅ Job 3dd8e24f-d5bb-4d3c-8b50-4614fb4f340b (tool_processing) added to queue
📊 Memory: 1101MB/1152MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
🔄 Processing job 3dd8e24f-d5bb-4d3c-8b50-4614fb4f340b (tool_processing)
📋 Status updated for job 3dd8e24f-d5bb-4d3c-8b50-4614fb4f340b: processing
🔄 Processing existing tool 55f05e79-8477-437f-a921-13df6316a4e0 for https://www.foodieprep.ai/
🎯 Creating job: web_scraping
 ○ Compiling /api/admin/bulk-processing/[id] ...
📋 Status updated for job be941eaa-6538-4543-9ddc-e9b5e0b509bd: pending
✅ Job be941eaa-6538-4543-9ddc-e9b5e0b509bd (web_scraping) added to queue
 ✓ Compiled /api/admin/bulk-processing/[id] in 645ms (3349 modules)
🔄 Processing job be941eaa-6538-4543-9ddc-e9b5e0b509bd (web_scraping)
✅ Loading configuration from JSONB column
🤖 Automated cleanup started (every 30 minutes)
📊 Cleanup monitoring started (every 15 minutes)
🔄 Processing job be941eaa-6538-4543-9ddc-e9b5e0b509bd (web_scraping)
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 1533ms
✅ Loading configuration from JSONB column
📋 Status updated for job be941eaa-6538-4543-9ddc-e9b5e0b509bd: processing
🕷️ Enhanced web scraping job started for: https://www.foodieprep.ai/ (Tool ID: 55f05ee79-8477-437f-a921-13df6316a4e0)
🚀 Starting enhanced scraping for: https://www.foodieprep.ai/
🎯 COST-OPTIMIZED SCRAPING: https://www.foodieprep.ai/
🔧 Request parameters:
   token: 8e7e405f...
   url: https://www.foodieprep.ai/
   device: desktop
   output: markdown
   timeout: 15000
🔗 Scraping URL: https://www.foodieprep.ai/
📡 Request URL: https://api.scrape.do/?token=8e7e405ff81145c4afe447610ddb9a7f785f494dddc&url=https%3A%2F%2Fwww.foodieprep.ai%2F&device=desktop&output=markdown&timeout=15000
📋 Status updated for job be941eaa-6538-4543-9ddc-e9b5e0b509bd: processing
🕷️ Enhanced web scraping job started for: https://www.foodieprep.ai/ (Tool ID: 55f05ee79-8477-437f-a921-13df6316a4e0)
🚀 Starting enhanced scraping for: https://www.foodieprep.ai/
🎯 COST-OPTIMIZED SCRAPING: https://www.foodieprep.ai/
🔧 Request parameters:
   token: 8e7e405f...
   url: https://www.foodieprep.ai/
   device: desktop
   output: markdown
   timeout: 15000
🔗 Scraping URL: https://www.foodieprep.ai/
📡 Request URL: https://api.scrape.do/?token=8e7e405ff81145c4afe447610ddb9a7f785f494dddc&url=https%3A%2F%2Fwww.foodieprep.ai%2F&device=desktop&output=markdown&timeout=15000
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 271ms
📊 Memory: 1141MB/1210MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
✅ Loading configuration from JSONB column
📊 Memory: 1141MB/1210MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 286ms    
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 277ms    
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 285ms    
✅ Loading configuration from JSONB column
📊 Memory: 1139MB/1210MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 272ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 289ms
📊 Memory: 1145MB/1210MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 279ms
✅ Loading configuration from JSONB column
📊 Memory: 1137MB/1210MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1137MB / 1210MB (1 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 300ms
✅ Loading configuration from JSONB column
📊 Memory: 1140MB/1210MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1140MB / 1210MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
❌ HTTP 502: Bad Gateway
📄 Error response body: {"URL":"https://www.foodieprep.ai/","StatusCode":502,"ErrorCode":90,"Message":["Error: cannot connect target url","Request failed and not charged. Please try again."],"PossibleCauses":["Make sure the url address is correct. If you are using query parameter, you need to use url encoding.","Check request headers with 'customHeaders=True' attribute.","Use 'Render=True' and render JavaScript like real Chrome Browser.","Use super gateway (residential \u0026 mobile proxies) with 'Super=True'","Try making a request through the country where your target site is hosted using 'GeoCode'."],"Contact":"If you continue to receive this error, please contact with <NAME_EMAIL>"}
Scraping error: {
  code: 'SERVER_ERROR',
  message: `HTTP 502: Bad Gateway - {"URL":"https://www.foodieprep.ai/","StatusCode":502,"ErrorCode":90,"Message":["Error: cannot connect target url","Request failed and not charged. Please try again."],"PossibleCauses":["Make sure the url address is correct. If you are using query parameter, you need to use url encoding.","Check request headers with 'customHeaders=True' attribute.","Use 'Render=True' and render JavaScript like real Chrome Browser.","Use super gateway (residential \\u0026 mobile proxies) with 'Super=True'","Try making a request through the country where your target site is hosted using 'GeoCode'."],"Contact":"If you continue to receive this error, please contact with <NAME_EMAIL>"}`,
  url: 'https://www.foodieprep.ai/',
  timestamp: '2025-06-24T08:03:42.950Z',
  retryable: false,
  context: {
    options: {
      useResidentialProxy: false,
      enableJSRendering: false,
      outputFormat: 'markdown',
      timeout: 15000,
      deviceType: 'desktop'
    }
  }
}
💡 Suggestion: Try using enhanced scraping with render=true for this URL
⚡ ENHANCING: https://www.foodieprep.ai/ (5 credits) - AI Quality Focus - Probability: 1.40, Expected Quality: 8x improvement
🔧 Request parameters:
   token: 8e7e405f...
   url: https://www.foodieprep.ai/
   render: true
   device: desktop
   waitUntil: networkidle2
   customWait: 3000
   output: markdown
   blockResources: true
   timeout: 45000
🔗 Scraping URL: https://www.foodieprep.ai/
📡 Request URL: https://api.scrape.do/?token=8e7e405ff81145c4afe447610ddb9a7f785f494dddc&url=https%3A%2F%2Fwww.foodieprep.ai%2F&render=true&device=desktop&waitUntil=networkidle2&customWait=3000&output=markdown&blockResources=true&timeout=45000
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 367ms
✅ Loading configuration from JSONB column
📊 Memory: 1144MB/1210MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1144MB / 1210MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
✅ Scraping successful - 7073 characters received
💰 COST-SAVE: Using basic content despite quality issues (AI Quality Focus - Probability: 0.30, Expected Quality: 1.5x improvement)
💰 COST-OPTIMIZER: Using basic content as determined by cost analysis
🔍 Text analysis: 711 words, 136 sentences, length: 6975
Content analysis: Content sufficient (ratio: 0.99, confidence: 70) - KEEPING content 
💰 MEDIA-OPTIMIZED: Extracting media from basic content without additional scraping  
✅ Enhanced scraping completed in 22642ms
📊 Memory: 1145MB/1210MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
Database storage failed: {}
📁 Content stored: G:\projects\dudeai1\data\scraped-content\www.foodieprep.ai\www.foodieprep.ai__2025-06-24T08-03-45-863Z.md
✅ Enhanced web scraping completed for: https://www.foodieprep.ai/ (1 credits)       
 ✓ Compiled in 470ms (2243 modules)
 GET /admin/bulk 200 in 205ms
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 510ms
 ✓ Compiled in 285ms (2243 modules)
 GET /admin/bulk 200 in 12ms
📊 Memory: 1243MB/1296MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1243MB / 1296MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
📊 Progress updated for job be941eaa-6538-4543-9ddc-e9b5e0b509bd: 100% - Job completed successfully
✅ Job be941eaa-6538-4543-9ddc-e9b5e0b509bd completed successfully
🎯 Creating job: content_generation
✅ Loading configuration from JSONB column
📋 Status updated for job 28236bab-89ee-446f-bd75-80b84b6957af: pending
✅ Job 28236bab-89ee-446f-bd75-80b84b6957af (content_generation) added to queue
🔄 Processing job 28236bab-89ee-446f-bd75-80b84b6957af (content_generation)
📋 Status updated for job 28236bab-89ee-446f-bd75-80b84b6957af: processing
Starting content generation pipeline for: https://www.foodieprep.ai/
Tool ID: 55f05e79-8477-437f-a921-13df6316a4e0
Content size: 7150 characters
Options: {
  skipValidation: false,
  requireEditorialReview: false,
  autoApprove: false,
  qualityThreshold: 80,
  priority: 'normal'
}
Starting content generation pipeline for tool: 55f05e79-8477-437f-a921-13df6316a4e0  
[2025-06-24T08:03:49.239Z] INFO: Using AI Dude methodology with gpt-4o-2024-11-20 | component=ai-system operation=ai-dude-generation toolUrl=https://www.foodieprep.ai/ model=gpt-4o-2024-11-20 provider=openai methodology=ai_dude
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 282ms
✅ Loading configuration from JSONB column
📊 Memory: 1147MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1148MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1150MB / 1279MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 695ms
✅ Loading configuration from JSONB column
📊 Memory: 1142MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 270ms
✅ Loading configuration from JSONB column
📊 Memory: 1143MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 297ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 275ms
✅ Loading configuration from JSONB column
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 351ms
✅ Loading configuration from JSONB column
📊 Relevance Score Breakdown:
      Concept Overlap: 0.18
      Domain Relevance: 0.90
      Structural Similarity: 0.33
      Final Score: 0.44
📊 Memory: 1141MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
Pipeline completed successfully for tool: 55f05e79-8477-437f-a921-13df6316a4e0
Status: pending_manual_review, Workflow: manual_review
Quality Score: 95
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 278ms
📊 Progress updated for job 28236bab-89ee-446f-bd75-80b84b6957af: 100% - Job completed successfully
✅ Job 28236bab-89ee-446f-bd75-80b84b6957af completed successfully
✅ Loading configuration from JSONB column
✅ Updated tool 55f05e79-8477-437f-a921-13df6316a4e0 with generated content
📊 Progress updated for job 3dd8e24f-d5bb-4d3c-8b50-4614fb4f340b: 100% - Job completed successfully
✅ Job 3dd8e24f-d5bb-4d3c-8b50-4614fb4f340b completed successfully
🔒 Acquired lock lock_7383e739-e6f4-4a77-9c51-2cdc825a23b7_1750752245339_galc48f4p for job 7383e739-e6f4-4a77-9c51-2cdc825a23b7
✅ Updated job 7383e739-e6f4-4a77-9c51-2cdc825a23b7 progress: 1/1 (v2)
🔓 Released lock lock_7383e739-e6f4-4a77-9c51-2cdc825a23b7_1750752245339_galc48f4p for job 7383e739-e6f4-4a77-9c51-2cdc825a23b7
✅ Completed job 7383e739-e6f4-4a77-9c51-2cdc825a23b7 (v3)
🧹 Cleaning up 0 resources for job 7383e739-e6f4-4a77-9c51-2cdc825a23b7
✅ Job 7383e739-e6f4-4a77-9c51-2cdc825a23b7 cleaned up successfully
✅ Bulk job 7383e739-e6f4-4a77-9c51-2cdc825a23b7 completed successfully
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 288ms
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 266ms
📊 API returning 2 bulk processing jobs with snake_case fields
 GET /api/admin/bulk-processing 200 in 300ms
 GET /api/admin/bulk-processing/7383e739-e6f4-4a77-9c51-2cdc825a23b7 200 in 290ms
📊 Memory: 1146MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
✅ Loading configuration from JSONB column
📊 Memory: 1146MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1147MB / 1279MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
📊 Memory: 1147MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1147MB / 1279MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
Scrape attempt 1 failed, retrying in 2000ms...
📊 Memory: 1147MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1147MB / 1279MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
📊 Memory: 1148MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1148MB/1279MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1148MB / 1279MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
📊 Memory: 1106MB/1142MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1114MB/1157MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1114MB / 1157MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
✅ Loading configuration from JSONB column
📊 Memory: 1115MB/1157MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1115MB/1157MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1116MB/1157MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
Enhanced web scraping failed: Error: Request timeout after 70000ms
    at WebScrapingHandler.executeWebScraping (src\lib\jobs\handlers\web-scraping.ts:105:14)
    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.scraping.handleWithRetry._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\web-scraping.ts:39:15)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13)       
    at async Object.handleWithRetry (src\lib\error-handling\index.ts:319:13)
    at async WebScrapingHandler.handle (src\lib\jobs\handlers\web-scraping.ts:37:11) 
    at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)     
  103 |
  104 |       if (!result.success) {
> 105 |         throw new Error(result.error || 'Enhanced scraping failed');
      |              ^
  106 |       }
  107 |
  108 |       // Convert enhanced result to legacy job format for backward compatibility
Enhanced error context: {
  type: 'Error',
  message: 'Request timeout after 70000ms',
  stack: 'Error: Request timeout after 70000ms\n' +
    '    at WebScrapingHandler.executeWebScraping (webpack-internal:///(rsc)/./src/lib/jobs/handlers/web-scraping.ts:103:23)\n' +
    '    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.scraping.handleWithRetry._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (webpack-internal:///(rsc)/./src/lib/jobs/handlers/web-scraping.ts:14:20)\n' +
    '    at async Object.withErrorHandling (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:110:20)\n' +
    '    at async Object.handleWithRetry (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:280:20)\n' +
    '    at async WebScrapingHandler.handle (webpack-internal:///(rsc)/./src/lib/jobs/handlers/web-scraping.ts:13:16)\n' +
    '    at async EnhancedJobQueue.processJob (webpack-internal:///(rsc)/./src/lib/jobs/enhanced-queue.ts:401:28)',
  context: {
    operation: 'web_scraping_job',
    provider: 'scrape.do',
    toolId: undefined,
    jobId: undefined,
    userId: undefined,
    attempt: undefined,
    maxRetries: undefined
  },
  timestamp: '2025-06-24T08:04:32.912Z',
  retryable: true
}
ERROR TRACKED: {
  id: 'err_1750752272913_ccc4jmdhp',
  type: 'UNKNOWN_ERROR',
  category: 'network',
  severity: 'low',
  message: 'Enhanced web scraping failed: Request timeout after 70000ms',
  context: {
    operation: 'web_scraping_job',
    provider: 'scrape.do',
    toolId: undefined,
    jobId: undefined
  },
  timestamp: '2025-06-24T08:04:32.913Z'
}
ERROR EVENT: {
  error: {
    id: 'err_1750752272913_ccc4jmdhp',
    type: 'UNKNOWN_ERROR',
    category: 'network',
    severity: 'low',
    message: 'Enhanced web scraping failed: Request timeout after 70000ms',
    stack: 'Error: Enhanced web scraping failed: Request timeout after 70000ms\n' +  
      '    at WebScrapingHandler.executeWebScraping (webpack-internal:///(rsc)/./src/lib/jobs/handlers/web-scraping.ts:132:19)\n' +
      '    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.scraping.handleWithRetry._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (webpack-internal:///(rsc)/./src/lib/jobs/handlers/web-scraping.ts:14:20)\n' +
      '    at async Object.withErrorHandling (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:110:20)\n' +
      '    at async Object.handleWithRetry (webpack-internal:///(rsc)/./src/lib/error-handling/index.ts:280:20)\n' +
      '    at async WebScrapingHandler.handle (webpack-internal:///(rsc)/./src/lib/jobs/handlers/web-scraping.ts:13:16)\n' +
      '    at async EnhancedJobQueue.processJob (webpack-internal:///(rsc)/./src/lib/jobs/enhanced-queue.ts:401:28)',
    context: {
      operation: 'web_scraping_job',
      provider: 'scrape.do',
      toolId: undefined,
      jobId: undefined,
      userId: undefined,
      metadata: [Object],
      retryOperation: [AsyncFunction (anonymous)],
      maxRetries: 3
    },
    classification: {
      category: 'network',
      severity: 'low',
      retryable: true,
      autoRecoverable: true,
      requiresManualIntervention: false,
      affectedSystems: [Array],
      estimatedImpact: [Object]
    },
    timestamp: '2025-06-24T08:04:32.913Z',
    originalError: Error: Enhanced web scraping failed: Request timeout after 70000ms
        at WebScrapingHandler.executeWebScraping (src\lib\jobs\handlers\web-scraping.ts:139:12)
        at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.scraping.handleWithRetry._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\web-scraping.ts:39:15)
        at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13)   
        at async Object.handleWithRetry (src\lib\error-handling\index.ts:319:13)     
        at async WebScrapingHandler.handle (src\lib\jobs\handlers\web-scraping.ts:37:11)
        at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21) 
      137 |       }));
      138 |
    > 139 |       throw new Error(`Enhanced web scraping failed: ${error instanceof Error ? error.message : String(error)}`);
          |            ^
      140 |     }
      141 |   }
      142 |
  },
  context: {
    operation: 'web_scraping_job',
    provider: 'scrape.do',
    toolId: undefined,
    jobId: undefined,
    userId: undefined,
    metadata: {
      url: 'https://www.foodieprep.ai/',
      jobId: 'be941eaa-6538-4543-9ddc-e9b5e0b509bd',
      options: [Object]
    },
    retryOperation: [AsyncFunction (anonymous)],
    maxRetries: 3
  },
  recovery: {
    success: false,
    strategy: 'none',
    error: 'No applicable recovery strategy found',
    timestamp: '2025-06-24T08:04:32.913Z',
    requiresManualIntervention: true
  },
  timestamp: '2025-06-24T08:04:32.913Z'
}
❌ Job be941eaa-6538-4543-9ddc-e9b5e0b509bd failed: Error: Enhanced web scraping failed: Request timeout after 70000ms
    at WebScrapingHandler.executeWebScraping (src\lib\jobs\handlers\web-scraping.ts:139:12)
    at async _error_handling__WEBPACK_IMPORTED_MODULE_1__.CommonErrorHandlers.scraping.handleWithRetry._error_handling__WEBPACK_IMPORTED_MODULE_1__.ErrorHandlingUtils.createErrorContext.operation (src\lib\jobs\handlers\web-scraping.ts:39:15)
    at async Object.withErrorHandling (src\lib\error-handling\index.ts:100:13)       
    at async Object.handleWithRetry (src\lib\error-handling\index.ts:319:13)
    at async WebScrapingHandler.handle (src\lib\jobs\handlers\web-scraping.ts:37:11) 
    at async EnhancedJobQueue.processJob (src\lib\jobs\enhanced-queue.ts:449:21)     
  137 |       }));
  138 |
> 139 |       throw new Error(`Enhanced web scraping failed: ${error instanceof Error ? error.message : String(error)}`);
      |            ^
  140 |     }
  141 |   }
  142 |

  ⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
❌ HTTP 429: Too Many Requests
📄 Error response body: {"URL":"https://dashboard.scrape.do","StatusCode":429,"Message":["You are sending too fast request. You can send slow request or upgrade your plan. <NAME_EMAIL> for subscription plan."],"PossibleCauses":["Please check your account details in Scrape.do dashboard."],"Contact":"Please don't hesitate to contact with <NAME_EMAIL>."}
Scraping error: {
  code: 'CLIENT_ERROR',
  message: `HTTP 429: Too Many Requests - {"URL":"https://dashboard.scrape.do","StatusCode":429,"Message":["You are sending too fast request. You can send slow request or upgrade your plan. <NAME_EMAIL> for subscription plan."],"PossibleCauses":["Please check your account details in Scrape.do dashboard."],"Contact":"Please don't hesitate to contact with <NAME_EMAIL>."}`,
  url: 'https://www.foodieprep.ai/',
  timestamp: '2025-06-24T08:05:29.861Z',
  retryable: false,
  context: {
    options: {
      enableJSRendering: true,
      captureScreenshot: true,
      fullPageScreenshot: false,
      blockResources: true,
      timeout: 50000,
      deviceType: 'desktop',
      customWaitTime: 2000,
      returnJSON: true
    }
  }
}
✅ Enhanced scraping completed in 127007ms
Database storage failed: {}
📁 Content stored: G:\projects\dudeai1\data\scraped-content\www.foodieprep.ai\www.foodieprep.ai__2025-06-24T08-05-30-140Z.md
 ✓ Compiled in 454ms (2243 modules)
 GET /admin/bulk 200 in 33ms
📊 Memory: 1085MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1086MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1086MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1086MB / 1176MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
📊 Memory: 1086MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1086MB / 1176MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
📊 Memory: 1087MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1087MB / 1176MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
📊 Memory: 1087MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1087MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1087MB / 1176MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
📊 Memory: 1088MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1088MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory usage: 1088MB / 1176MB (0 active jobs)
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency cleanup...
🚨 Emergency cleanup completed (0 jobs cleaned)
✅ Loading configuration from JSONB column
📊 Memory: 1089MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1089MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1089MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1090MB/1176MB, Resources: 0, Jobs: 0
⚠️ High memory usage detected, triggering cleanup
🚨 Performing emergency resource cleanup...
🚨 Emergency cleanup completed (0 resources cleaned)
📊 Memory: 1090MB/1176MB, Resources: 0, Jobs: 0