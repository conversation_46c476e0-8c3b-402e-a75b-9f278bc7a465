'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/Button';
import { Job, JobStatus, JobType } from '@/lib/jobs/types';

interface JobListTableProps {
  jobs: Job[];
  selectedJobIds: string[];
  onJobSelect: (job: Job) => void;
  onJobSelection: (jobId: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  onJobAction: (action: 'pause' | 'resume' | 'stop' | 'retry' | 'delete', jobId: string) => void;
  viewMode: 'table' | 'cards';
  isConnected?: boolean;
}

interface SortConfig {
  key: keyof Job;
  direction: 'asc' | 'desc';
}

/**
 * Job Status Badge Component
 */
function JobStatusBadge({ status, job }: { status: JobStatus; job?: Job }): React.JSX.Element {
  // Check if a retrying job is stuck
  const isStuck = job && status === JobStatus.RETRYING && (() => {
    const now = new Date();
    const updatedAt = new Date(job.updatedAt);
    const minutesSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60);
    return minutesSinceUpdate > 15;
  })();

  const statusConfig = {
    [JobStatus.PENDING]: { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30', icon: '⏳' },
    [JobStatus.PROCESSING]: { color: 'bg-blue-500/20 text-blue-400 border-blue-500/30', icon: '⚡' },
    [JobStatus.COMPLETED]: { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: '✅' },
    [JobStatus.FAILED]: { color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: '❌' },
    [JobStatus.RETRYING]: {
      color: isStuck ? 'bg-red-500/20 text-red-400 border-red-500/30' : 'bg-orange-500/20 text-orange-400 border-orange-500/30',
      icon: isStuck ? '⚠️' : '🔄'
    },
    [JobStatus.CANCELLED]: { color: 'bg-gray-500/20 text-gray-400 border-gray-500/30', icon: '⏹️' },
    [JobStatus.PAUSED]: { color: 'bg-purple-500/20 text-purple-400 border-purple-500/30', icon: '⏸️' },
    [JobStatus.STOPPING]: { color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: '🛑' },
    [JobStatus.STOPPED]: { color: 'bg-gray-500/20 text-gray-400 border-gray-500/30', icon: '⏹️' },
  };

  const config = statusConfig[status] || statusConfig[JobStatus.PENDING];
  const displayStatus = isStuck ? 'Stuck' : status.charAt(0).toUpperCase() + status.slice(1);

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${config.color}`}
      title={isStuck ? 'Job appears to be stuck - retry available' : ''}
    >
      <span className="mr-1">{config.icon}</span>
      {displayStatus}
    </span>
  );
}

/**
 * Job Type Badge Component
 */
function JobTypeBadge({ type }: { type: JobType }): React.JSX.Element {
  const typeConfig = {
    [JobType.TOOL_SUBMISSION]: { color: 'bg-blue-500/20 text-blue-400', icon: '📝' },
    [JobType.CONTENT_GENERATION]: { color: 'bg-purple-500/20 text-purple-400', icon: '🤖' },
    [JobType.WEB_SCRAPING]: { color: 'bg-green-500/20 text-green-400', icon: '🕷️' },
    [JobType.EMAIL_NOTIFICATION]: { color: 'bg-yellow-500/20 text-yellow-400', icon: '📧' },
    [JobType.TOOL_PROCESSING]: { color: 'bg-orange-500/20 text-orange-400', icon: '⚙️' },
    [JobType.SCREENSHOT_CAPTURE]: { color: 'bg-cyan-500/20 text-cyan-400', icon: '📸' },
    [JobType.FAVICON_EXTRACTION]: { color: 'bg-pink-500/20 text-pink-400', icon: '🎨' },
    [JobType.BULK_PROCESSING]: { color: 'bg-indigo-500/20 text-indigo-400', icon: '📦' },
  };

  const config = typeConfig[type] || typeConfig[JobType.TOOL_SUBMISSION];

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      <span className="mr-1">{config.icon}</span>
      {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
    </span>
  );
}

/**
 * Progress Bar Component
 */
function ProgressBar({ progress }: { progress?: number }): React.JSX.Element {
  const progressValue = progress || 0;
  
  return (
    <div className="w-full bg-zinc-700 rounded-full h-2">
      <div 
        className="bg-orange-500 h-2 rounded-full transition-all duration-300"
        style={{ width: `${progressValue}%` }}
      />
    </div>
  );
}

/**
 * Job Actions Menu Component
 */
function JobActionsMenu({
  job,
  onJobAction,
  isStuckRetrying,
  isStalePending,
  canJobBeRetried
}: {
  job: Job;
  onJobAction: (action: 'pause' | 'resume' | 'stop' | 'retry' | 'delete', jobId: string) => void;
  isStuckRetrying: (job: Job) => boolean;
  isStalePending: (job: Job) => boolean;
  canJobBeRetried: (job: Job) => boolean;
}): React.JSX.Element {
  const [showMenu, setShowMenu] = useState(false);

  const getAvailableActions = (status: JobStatus) => {
    switch (status) {
      case JobStatus.PROCESSING:
        return ['pause', 'stop'];
      case JobStatus.PAUSED:
        return ['resume', 'stop'];
      case JobStatus.FAILED:
        return ['retry', 'delete'];
      case JobStatus.RETRYING:
        // Allow retry for stuck retrying jobs, otherwise just delete
        return isStuckRetrying(job) ? ['retry', 'delete'] : ['delete'];
      case JobStatus.COMPLETED:
        return ['delete'];
      case JobStatus.PENDING:
        // Allow retry for stale pending jobs
        const actions = ['stop', 'delete'];
        if (isStalePending(job)) {
          actions.unshift('retry'); // Add retry as first option
        }
        return actions;
      case JobStatus.STOPPED:
        // Stopped jobs can only be retried or deleted
        return ['retry', 'delete'];
      case JobStatus.CANCELLED:
        // Cancelled jobs can be retried or deleted
        return ['retry', 'delete'];
      case JobStatus.STOPPING:
        // Jobs that are stopping can only be deleted
        return ['delete'];
      default:
        return ['delete'];
    }
  };

  const actions = getAvailableActions(job.status);
  const isStuck = isStuckRetrying(job);

  const getRetryLabel = (): string => {
    if (job.status === JobStatus.FAILED) return '🔄 Retry';
    if (isStuckRetrying(job)) return '🔄 Force Retry';
    if (isStalePending(job)) return '🔄 Refresh';
    return '🔄 Retry';
  };

  const actionLabels = {
    pause: '⏸️ Pause',
    resume: '▶️ Resume',
    stop: '⏹️ Stop',
    retry: getRetryLabel(),
    delete: '🗑️ Delete'
  };

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowMenu(!showMenu)}
        className={`text-xs ${isStuck ? 'border-orange-500 text-orange-400' : ''}`}
        title={isStuck ? 'Job appears to be stuck - retry available' : ''}
      >
        {isStuck ? '⚠️' : '⋯'}
      </Button>
      
      {showMenu && (
        <div className="absolute right-0 mt-2 w-32 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg z-10">
          {actions.map(action => (
            <button
              key={action}
              onClick={() => {
                onJobAction(action as any, job.id);
                setShowMenu(false);
              }}
              className="w-full text-left px-3 py-2 text-sm text-white hover:bg-zinc-700 first:rounded-t-lg last:rounded-b-lg"
            >
              {actionLabels[action as keyof typeof actionLabels]}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

/**
 * Job List Table Component
 * 
 * Displays jobs in a sortable, selectable table format.
 * Features:
 * - Sortable columns
 * - Row selection with checkboxes
 * - Status and type badges
 * - Progress indicators
 * - Action menus for individual jobs
 * - Responsive design
 */
export function JobListTable({
  jobs,
  selectedJobIds,
  onJobSelect,
  onJobSelection,
  onSelectAll,
  onJobAction,
  viewMode,
  isConnected = false
}: JobListTableProps): React.JSX.Element {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'createdAt', direction: 'desc' });

  // Helper functions for job state analysis (shared across components)
  const isStuckRetrying = (job: Job): boolean => {
    if (job.status !== JobStatus.RETRYING) return false;

    const now = new Date();
    const updatedAt = new Date(job.updatedAt);
    const minutesSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60);

    return minutesSinceUpdate > 15; // Consider stuck if retrying for more than 15 minutes
  };

  const isStalePending = (job: Job): boolean => {
    if (job.status !== JobStatus.PENDING) return false;

    const now = new Date();
    const updatedAt = new Date(job.updatedAt);
    const minutesSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60);

    return minutesSinceUpdate > 30; // Consider stale if pending for more than 30 minutes
  };

  const canJobBeRetried = (job: Job): boolean => {
    // Jobs that are currently processing cannot be retried
    if (job.status === JobStatus.PROCESSING) return false;

    // Completed jobs cannot be retried
    if (job.status === JobStatus.COMPLETED) return false;

    // All other statuses can be retried (including stopped jobs)
    return true;
  };

  // Sort jobs based on current sort configuration
  const sortedJobs = useMemo(() => {
    return [...jobs].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [jobs, sortConfig]);

  // Handle column sorting
  const handleSort = (key: keyof Job) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Format date for display
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleString();
  };

  // Format duration
  const formatDuration = (start: Date | string, end?: Date | string) => {
    const startTime = new Date(start).getTime();
    const endTime = end ? new Date(end).getTime() : Date.now();
    const duration = endTime - startTime;
    
    if (duration < 60000) return `${Math.round(duration / 1000)}s`;
    if (duration < 3600000) return `${Math.round(duration / 60000)}m`;
    return `${Math.round(duration / 3600000)}h`;
  };

  // Check if all jobs are selected
  const allSelected = jobs.length > 0 && selectedJobIds.length === jobs.length;
  const someSelected = selectedJobIds.length > 0 && selectedJobIds.length < jobs.length;

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b border-zinc-700">
            <th className="text-left p-4 w-12">
              <input
                type="checkbox"
                checked={allSelected}
                ref={input => {
                  if (input) input.indeterminate = someSelected;
                }}
                onChange={(e) => onSelectAll(e.target.checked)}
                className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
              />
            </th>
            <th 
              className="text-left p-4 cursor-pointer hover:text-orange-400 transition-colors"
              onClick={() => handleSort('type')}
            >
              Type {sortConfig.key === 'type' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
            </th>
            <th 
              className="text-left p-4 cursor-pointer hover:text-orange-400 transition-colors"
              onClick={() => handleSort('status')}
            >
              Status {sortConfig.key === 'status' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
            </th>
            <th className="text-left p-4">Progress</th>
            <th 
              className="text-left p-4 cursor-pointer hover:text-orange-400 transition-colors"
              onClick={() => handleSort('createdAt')}
            >
              Created {sortConfig.key === 'createdAt' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
            </th>
            <th className="text-left p-4">Duration</th>
            <th className="text-left p-4">Tool ID</th>
            <th className="text-left p-4 w-20">Actions</th>
          </tr>
        </thead>
        <tbody>
          {sortedJobs.map((job) => (
            <tr 
              key={job.id}
              className={`border-b border-zinc-700/50 hover:bg-zinc-700/30 transition-colors cursor-pointer ${
                selectedJobIds.includes(job.id) ? 'bg-orange-500/10' : ''
              }`}
              onClick={() => onJobSelect(job)}
            >
              <td className="p-4" onClick={(e) => e.stopPropagation()}>
                <input
                  type="checkbox"
                  checked={selectedJobIds.includes(job.id)}
                  onChange={(e) => onJobSelection(job.id, e.target.checked)}
                  className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
                />
              </td>
              <td className="p-4">
                <JobTypeBadge type={job.type} />
              </td>
              <td className="p-4">
                <JobStatusBadge status={job.status} job={job} />
              </td>
              <td className="p-4">
                <div className="space-y-1">
                  <ProgressBar progress={job.progress} />
                  <div className="text-xs text-gray-400">
                    {job.progress || 0}%
                  </div>
                </div>
              </td>
              <td className="p-4 text-sm text-gray-300">
                {formatDate(job.createdAt)}
              </td>
              <td className="p-4 text-sm text-gray-300">
                {formatDuration(job.createdAt, job.completedAt)}
              </td>
              <td className="p-4 text-sm text-gray-300">
                <div className="max-w-32 truncate" title={job.toolId}>
                  {job.toolId || '-'}
                </div>
              </td>
              <td className="p-4" onClick={(e) => e.stopPropagation()}>
                <div className="flex items-center gap-2">
                  {/* Show prominent retry button for retryable jobs */}
                  {(() => {
                    const shouldShowRetryButton =
                      job.status === JobStatus.FAILED ||
                      isStuckRetrying(job) ||
                      isStalePending(job);

                    if (!shouldShowRetryButton) return null;

                    let buttonText = '🔄 Retry';
                    let buttonTitle = 'Retry job';

                    if (job.status === JobStatus.FAILED) {
                      buttonTitle = 'Retry failed job';
                    } else if (isStuckRetrying(job)) {
                      buttonText = '🔄 Force Retry';
                      buttonTitle = 'Force retry stuck job';
                    } else if (isStalePending(job)) {
                      buttonText = '🔄 Refresh';
                      buttonTitle = 'Refresh stale pending job';
                    }

                    return (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onJobAction('retry', job.id)}
                        className="text-xs bg-orange-500/10 border-orange-500/30 text-orange-400 hover:bg-orange-500/20"
                        title={buttonTitle}
                      >
                        {buttonText}
                      </Button>
                    );
                  })()}
                  <JobActionsMenu
                    job={job}
                    onJobAction={onJobAction}
                    isStuckRetrying={isStuckRetrying}
                    isStalePending={isStalePending}
                    canJobBeRetried={canJobBeRetried}
                  />
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default JobListTable;
