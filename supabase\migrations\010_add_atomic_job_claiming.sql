-- Migration 010: Add atomic job claiming function to prevent duplicate job processing
-- This function atomically claims pending jobs by updating their status to PROCESSING

-- Drop existing function first to allow changing return type
DROP FUNCTION IF EXISTS claim_pending_jobs(INTEGER, TIM<PERSON><PERSON>MP WITH TIME ZONE);

CREATE OR REPLACE FUNCTION claim_pending_jobs(
    max_jobs INTEGER DEFAULT 5,
    claim_time TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    id UUID,
    job_type VARCHAR(50),
    job_data JSONB,
    status VARCHAR(20),
    priority INTEGER,
    attempts INTEGER,
    max_attempts INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    scheduled_for TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_logs JSONB,
    result JSONB,
    progress INTEGER,
    tool_id VARCHAR(255)
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Atomically update pending jobs to processing status and return them
    -- This prevents race conditions where multiple queue instances claim the same jobs
    RETURN QUERY
    UPDATE ai_generation_jobs
    SET
        status = 'processing',
        updated_at = claim_time
    WHERE ai_generation_jobs.id IN (
        SELECT ai_generation_jobs.id
        FROM ai_generation_jobs
        WHERE ai_generation_jobs.status = 'pending'
        AND (
            ai_generation_jobs.scheduled_for IS NULL
            OR ai_generation_jobs.scheduled_for <= claim_time
        )
        ORDER BY 
            ai_generation_jobs.priority DESC,
            ai_generation_jobs.created_at ASC
        LIMIT max_jobs
        FOR UPDATE SKIP LOCKED  -- Skip jobs that are locked by other transactions
    )
    RETURNING
        ai_generation_jobs.id,
        ai_generation_jobs.job_type,
        ai_generation_jobs.job_data,
        ai_generation_jobs.status,
        ai_generation_jobs.priority,
        ai_generation_jobs.attempts,
        ai_generation_jobs.max_attempts,
        ai_generation_jobs.created_at,
        ai_generation_jobs.updated_at,
        ai_generation_jobs.scheduled_for,
        ai_generation_jobs.completed_at,
        ai_generation_jobs.error_logs,
        ai_generation_jobs.result,
        ai_generation_jobs.progress,
        ai_generation_jobs.tool_id;
END;
$$;

-- Grant execute permission to the service role
GRANT EXECUTE ON FUNCTION claim_pending_jobs(INTEGER, TIMESTAMP WITH TIME ZONE) TO service_role;

-- Add comment for documentation
COMMENT ON FUNCTION claim_pending_jobs IS 'Atomically claims pending jobs for processing to prevent duplicate execution by multiple queue instances';
